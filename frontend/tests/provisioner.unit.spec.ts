/**
 * Unit Tests for MCP Provisioner Service
 * 
 * Tests the automatic MCP API key provisioning functionality with mocked
 * Google Cloud services to ensure 100% branch coverage.
 */

import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { provisionMcpAccess } from '../src/services/mcpProvisioner';

// Mock Google Cloud clients
const mockCreateKey = vi.fn();
const mockCreateSecret = vi.fn();
const mockAddSecretVersion = vi.fn();
const mockSetIamPolicy = vi.fn();
const mockPromise = vi.fn();

// Mock the Google Cloud modules
vi.mock('@google-cloud/apikeys', () => ({
  ApiKeysClient: vi.fn().mockImplementation(() => ({
    createKey: mockCreateKey,
  })),
}));

vi.mock('@google-cloud/secret-manager', () => ({
  SecretManagerServiceClient: vi.fn().mockImplementation(() => ({
    createSecret: mockCreateSecret,
    addSecretVersion: mockAddSecretVersion,
    setIamPolicy: mockSetIamPolicy,
  })),
}));

// Mock console methods
const mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => {});
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

describe('MCP Provisioner Service', () => {
  const testTenantId = 'test-tenant-123';
  const testApiKey = 'test-api-key-value';
  const testSecretName = 'projects/texas-laws-personalinjury/secrets/mcp-key-test-tenant-123';
  const expectedSecretPath = `${testSecretName}/versions/latest`;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup default successful mock responses
    mockPromise.mockResolvedValue([{
      name: 'projects/texas-laws-personalinjury/locations/global/keys/mcp-rules-api-test-tenant-123',
      displayName: 'MCP Rules Engine API Key - test-tenant-123',
      keyString: testApiKey,
    }]);

    mockCreateKey.mockReturnValue([{ promise: mockPromise }]);

    mockCreateSecret.mockResolvedValue([{
      name: testSecretName,
    }]);

    mockAddSecretVersion.mockResolvedValue([{
      name: `${testSecretName}/versions/1`,
    }]);

    mockSetIamPolicy.mockResolvedValue([{}]);
  });

  afterEach(() => {
    mockConsoleLog.mockRestore();
    mockConsoleError.mockRestore();
  });

  describe('provisionMcpAccess', () => {
    it('should successfully provision MCP access and return secret path', async () => {
      const result = await provisionMcpAccess(testTenantId);

      expect(result).toBe(expectedSecretPath);
      
      // Verify API key creation
      expect(mockCreateKey).toHaveBeenCalledWith({
        parent: 'projects/texas-laws-personalinjury/locations/global',
        keyId: 'mcp-rules-api-test-tenant-123',
        key: {
          displayName: 'MCP Rules Engine API Key - test-tenant-123',
          restrictions: {},
        },
      });

      // Verify secret creation
      expect(mockCreateSecret).toHaveBeenCalledWith({
        parent: 'projects/texas-laws-personalinjury',
        secretId: 'mcp-key-test-tenant-123',
        secret: {
          replication: { automatic: {} },
          labels: {
            tenant: testTenantId,
            service: 'mcp-rules-engine',
            environment: 'default',
            managed_by: 'in-app-provisioner',
          },
        },
      });

      // Verify secret version creation
      expect(mockAddSecretVersion).toHaveBeenCalledWith({
        parent: testSecretName,
        payload: {
          data: Buffer.from(testApiKey, 'utf8'),
        },
      });

      // Verify IAM policy setting
      expect(mockSetIamPolicy).toHaveBeenCalledWith({
        resource: testSecretName,
        policy: {
          bindings: [{
            role: 'roles/secretmanager.secretAccessor',
            members: ['serviceAccount:<EMAIL>'],
          }],
        },
      });

      // Verify logging
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Successfully provisioned MCP access'),
        expect.objectContaining({
          secretPath: expectedSecretPath,
          tenantId: testTenantId,
        })
      );
    });

    it('should throw error when tenant ID is empty', async () => {
      await expect(provisionMcpAccess('')).rejects.toThrow(
        'Tenant ID is required for MCP provisioning'
      );
    });

    it('should throw error when tenant ID is null/undefined', async () => {
      await expect(provisionMcpAccess(null as any)).rejects.toThrow(
        'Tenant ID is required for MCP provisioning'
      );
      
      await expect(provisionMcpAccess(undefined as any)).rejects.toThrow(
        'Tenant ID is required for MCP provisioning'
      );
    });

    it('should handle API key creation failure', async () => {
      const error = new Error('API key creation failed');
      mockCreateKey.mockReturnValue([{ promise: vi.fn().mockRejectedValue(error) }]);

      await expect(provisionMcpAccess(testTenantId)).rejects.toThrow(
        `MCP provisioning failed for tenant ${testTenantId}: API key creation failed`
      );

      expect(mockConsoleError).toHaveBeenCalledWith(
        `Failed to provision MCP access for tenant: ${testTenantId}`,
        error
      );
    });

    it('should handle missing API key string', async () => {
      mockPromise.mockResolvedValue([{
        name: 'projects/texas-laws-personalinjury/locations/global/keys/mcp-rules-api-test-tenant-123',
        displayName: 'MCP Rules Engine API Key - test-tenant-123',
        keyString: null, // Missing key string
      }]);

      await expect(provisionMcpAccess(testTenantId)).rejects.toThrow(
        `MCP provisioning failed for tenant ${testTenantId}: Failed to create API key for tenant: ${testTenantId} in project: texas-laws-personalinjury`
      );
    });

    it('should handle secret creation failure', async () => {
      const error = new Error('Secret creation failed');
      mockCreateSecret.mockRejectedValue(error);

      await expect(provisionMcpAccess(testTenantId)).rejects.toThrow(
        `MCP provisioning failed for tenant ${testTenantId}: Secret creation failed`
      );
    });

    it('should handle secret version creation failure', async () => {
      const error = new Error('Secret version creation failed');
      mockAddSecretVersion.mockRejectedValue(error);

      await expect(provisionMcpAccess(testTenantId)).rejects.toThrow(
        `MCP provisioning failed for tenant ${testTenantId}: Secret version creation failed`
      );
    });

    it('should handle IAM policy setting failure', async () => {
      const error = new Error('IAM policy setting failed');
      mockSetIamPolicy.mockRejectedValue(error);

      await expect(provisionMcpAccess(testTenantId)).rejects.toThrow(
        `MCP provisioning failed for tenant ${testTenantId}: IAM policy setting failed`
      );
    });

    it('should handle Google Cloud client initialization failure', async () => {
      // Mock the ApiKeysClient constructor to throw
      const { ApiKeysClient } = await import('@google-cloud/apikeys');
      (ApiKeysClient as any).mockImplementationOnce(() => {
        throw new Error('Client initialization failed');
      });

      await expect(provisionMcpAccess(testTenantId)).rejects.toThrow(
        `MCP provisioning failed for tenant ${testTenantId}: Client initialization failed`
      );
    });

    it('should use correct environment variables', async () => {
      // Test with custom environment variables
      const originalMcpProject = process.env.MCP_PROJECT;
      process.env.MCP_PROJECT = 'custom-project';

      try {
        await provisionMcpAccess(testTenantId);

        expect(mockCreateKey).toHaveBeenCalledWith(
          expect.objectContaining({
            parent: 'projects/custom-project/locations/global',
          })
        );

        expect(mockCreateSecret).toHaveBeenCalledWith(
          expect.objectContaining({
            parent: 'projects/custom-project',
          })
        );
      } finally {
        process.env.MCP_PROJECT = originalMcpProject;
      }
    });

    it('should create API key with correct naming convention', async () => {
      const specialTenantId = 'tenant-with-special-chars_123';
      await provisionMcpAccess(specialTenantId);

      expect(mockCreateKey).toHaveBeenCalledWith(
        expect.objectContaining({
          keyId: 'mcp-rules-api-tenant-with-special-chars_123',
          key: expect.objectContaining({
            displayName: 'MCP Rules Engine API Key - tenant-with-special-chars_123',
          }),
        })
      );
    });

    it('should create secret with correct labels', async () => {
      await provisionMcpAccess(testTenantId);

      expect(mockCreateSecret).toHaveBeenCalledWith(
        expect.objectContaining({
          secret: expect.objectContaining({
            labels: {
              tenant: testTenantId,
              service: 'mcp-rules-engine',
              environment: 'default',
              managed_by: 'in-app-provisioner',
            },
          }),
        })
      );
    });

    it('should log all major steps', async () => {
      await provisionMcpAccess(testTenantId);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        `Creating API key for tenant: ${testTenantId} in project: texas-laws-personalinjury`
      );
      
      expect(mockConsoleLog).toHaveBeenCalledWith(
        `Storing API key in Secret Manager for tenant: ${testTenantId}`
      );
      
      expect(mockConsoleLog).toHaveBeenCalledWith(
        `Granting access to service accounts for tenant: ${testTenantId}`
      );
    });
  });
});
