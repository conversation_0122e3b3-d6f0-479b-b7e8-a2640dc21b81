# Tenant Auto-Provisioning System

This document describes the new in-app tenant auto-provisioning system that replaces the legacy Firestore-trigger Cloud Function with 100% automatic tenant onboarding for Supabase tenants.

## Overview

The auto-provisioning system automatically creates and configures MCP (Model Context Protocol) API keys for new tenants during the tenant creation process, eliminating the need for manual provisioning or external triggers.

### Key Features

- **100% Automatic**: No manual intervention required
- **In-App Integration**: Provisioning happens during tenant creation
- **Error Handling**: Comprehensive error handling with retry capabilities
- **Security**: Secure API key storage in Google Secret Manager
- **Monitoring**: Full logging and status tracking

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Tenant         │    │   MCP           │
│   Application   │───▶│   Service        │───▶│   Provisioner   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Supabase       │    │   Google Cloud  │
                       │   Database       │    │   (API Keys +   │
                       │   (tenants.firms)│    │   Secret Mgr)   │
                       └──────────────────┘    └─────────────────┘
```

## Components

### 1. MCP Provisioner Service (`services/mcpProvisioner.ts`)

**Purpose**: Creates API keys and stores them securely in Google Secret Manager.

**Key Function**: `provisionMcpAccess(tenantId: string): Promise<string>`

**Process**:
1. Creates API key in `texas-laws-personalinjury` project
2. Stores key in Secret Manager as `mcp-key-${tenantId}`
3. Grants access to AiLex service accounts
4. Returns secret path: `projects/.../secrets/.../versions/latest`

### 2. Tenant Service (`services/tenantService.ts`)

**Purpose**: Handles tenant creation with integrated MCP provisioning.

**Key Function**: `createTenant(input: TenantInput): Promise<TenantResult>`

**Process**:
1. Inserts tenant row with `mcp_status='pending_key'`
2. Calls `provisionMcpAccess(tenantId)`
3. Updates row with `secretPath` and `mcp_status='active'`

### 3. Database Schema

**Table**: `tenants.firms`

**MCP Fields**:
```sql
-- Google Secret Manager path for tenant-specific MCP API key
mcp_secret_path TEXT,

-- MCP provisioning status
mcp_status TEXT DEFAULT 'pending_key' 
CHECK (mcp_status IN ('pending_key', 'active', 'inactive', 'key_provisioning_failed'))
```

## Environment Variables

The following environment variables must be configured:

```bash
# Google Cloud Project for MCP resources
MCP_PROJECT=texas-laws-personalinjury

# Service account for MCP key provisioning
MCP_KEY_PROVISIONER_SA=<EMAIL>

# Supabase configuration
SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
SUPABASE_SERVICE_KEY=your-service-key
```

## IAM Requirements

### Service Account Permissions

The application's service account needs the following roles in the `texas-laws-personalinjury` project:

1. **API Keys Admin** (`roles/apikeys.admin`)
   - Create and manage API keys

2. **Secret Manager Admin** (`roles/secretmanager.admin`)
   - Create secrets and manage versions
   - Set IAM policies on secrets

3. **Service Account Token Creator** (`roles/iam.serviceAccountTokenCreator`)
   - For service account impersonation (if needed)

### Cross-Project Access

If running from a different project, configure service account impersonation:

```bash
# Grant impersonation permission
gcloud projects add-iam-policy-binding texas-laws-personalinjury \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/iam.serviceAccountTokenCreator"
```

## Usage Examples

### Basic Tenant Creation

```typescript
import { createClient } from '@supabase/supabase-js';
import { createTenantService } from './services/tenantService';

const supabase = createClient(url, key);
const tenantService = createTenantService(supabase);

const tenant = await tenantService.createTenant({
  name: 'Smith & Associates Law Firm',
  state_bar_number: 'TX123456',
  firm_type: 'Partnership',
  primary_email: '<EMAIL>',
  phone: '******-123-4567',
  address: {
    street: '123 Main St',
    city: 'Austin',
    state: 'TX',
    zip_code: '78701',
  },
  practice_areas: ['Personal Injury', 'Criminal Defense'],
});

console.log('Tenant created:', tenant.tenant_id);
console.log('MCP Status:', tenant.mcp_status); // 'active'
console.log('Secret Path:', tenant.mcp_secret_path);
```

### Error Handling

```typescript
try {
  const tenant = await tenantService.createTenant(input);
  // Success - tenant is active with MCP access
} catch (error) {
  console.error('Tenant creation failed:', error.message);
  // Check database for tenant with mcp_status='key_provisioning_failed'
}
```

### Retry Failed Provisioning

```typescript
// For tenants with mcp_status='key_provisioning_failed'
const tenant = await tenantService.retryMcpProvisioning(tenantId);
console.log('Retry result:', tenant.mcp_status);
```

## Monitoring and Troubleshooting

### Status Tracking

Monitor tenant provisioning status:

```sql
-- Check provisioning status
SELECT tenant_id, name, mcp_status, mcp_secret_path, updated_at 
FROM tenants.firms 
WHERE mcp_status != 'active';

-- Count by status
SELECT mcp_status, COUNT(*) 
FROM tenants.firms 
GROUP BY mcp_status;
```

### Common Issues

1. **Permission Errors**
   - Verify service account has required roles
   - Check cross-project IAM bindings

2. **API Key Creation Failures**
   - Verify API Keys API is enabled
   - Check project quotas and limits

3. **Secret Manager Errors**
   - Verify Secret Manager API is enabled
   - Check secret naming conflicts

### Logs

Application logs include:
- Tenant creation start/completion
- MCP provisioning steps
- Error details with tenant context
- Secret paths and status updates

## Migration from Legacy System

### Differences from Cloud Function

| Aspect | Legacy (Cloud Function) | New (In-App) |
|--------|------------------------|--------------|
| Trigger | Firestore document creation | Direct API call |
| Timing | Asynchronous (eventual) | Synchronous (immediate) |
| Error Handling | Limited visibility | Full error propagation |
| Retry Logic | Manual re-trigger | Built-in retry method |
| Integration | External webhook | Native service integration |

### Migration Steps

1. **Deploy New Services**: Deploy the new tenant and MCP provisioner services
2. **Update Frontend**: Integrate new tenant creation flow
3. **Test Thoroughly**: Verify end-to-end tenant creation
4. **Remove Legacy**: Disable Cloud Function triggers
5. **Clean Up**: Remove unused Firestore and Terraform code

## Testing

### Unit Tests

Run the comprehensive test suite:

```bash
npm test tests/provisioner.unit.spec.ts
```

Tests cover:
- Successful provisioning flow
- Error handling scenarios
- Edge cases and validation
- Mock Google Cloud services
- 100% branch coverage

### Integration Testing

Test with actual Google Cloud services:

```typescript
// Create test tenant
const testTenant = await tenantService.createTenant({
  name: 'Test Firm',
  // ... other required fields
});

// Verify secret exists
const secretClient = new SecretManagerServiceClient();
const [secret] = await secretClient.accessSecretVersion({
  name: testTenant.mcp_secret_path,
});

console.log('Secret retrieved successfully');
```

## Pre-Deployment Checklist

Before pushing to staging, verify these critical items:

### 1. Backend Service Account IAM ⚠️ CRITICAL
**Why**: Needs `apikeys.admin` and `secretmanager.admin` in `texas-laws-personalinjury`
```bash
# Verify current permissions
gcloud projects get-iam-policy texas-laws-personalinjury | grep <your-SA>

# Add required roles if missing
gcloud projects add-iam-policy-binding texas-laws-personalinjury \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/apikeys.admin"

gcloud projects add-iam-policy-binding texas-laws-personalinjury \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.admin"
```

### 2. API Key Restrictions 🔒 SECURITY
**Why**: Infinite keys are OK for now, but add restrictions later
- **Console**: API & Services → Credentials → Key restrictions
- **Recommendation**: Add "HTTP referrers = *" or IP lock for production

### 3. Secret Access Latency ⚡ PERFORMANCE
**Why**: First deadline request per tenant does a secret fetch; cache result to keep P95 < 700ms
- **Check**: Is result already cached in `deadlinesTool`?
- **Fix**: Add 10-min memoize if not cached

### 4. Quota Limits 📊 SCALABILITY
**Why**: Default API-Keys quota is 10 keys/sec; fine for onboarding but watch bulk imports
- **Monitor**: Console → Monitoring → Quotas → API Keys
- **Alert**: Set up quota alerts at 80% usage

### 5. Rotation Job 🔄 NICE-TO-HAVE
**Why**: Rotate keys yearly to satisfy security audits
- **Implementation**: Cloud Scheduler → call `rotateMcpKey(tenantId)`
- **Priority**: Low (can be added post-launch)

### Quick Verification Script

Run the verification script to check all requirements:

```bash
cd frontend
npm run verify-mcp-setup
```

## Security Considerations

1. **API Key Scope**: API keys are created without specific restrictions (add restrictions in production)
2. **Secret Access**: Limited to authorized AiLex service accounts
3. **Audit Trail**: All operations are logged with tenant context
4. **Encryption**: Secrets are encrypted at rest by Google Cloud
5. **Access Control**: IAM policies control secret access
6. **Key Rotation**: Implement yearly rotation for security compliance

## Performance

- **Provisioning Time**: ~5-10 seconds per tenant
- **Concurrent Limits**: Limited by Google Cloud API quotas (10 keys/sec default)
- **Retry Strategy**: Exponential backoff for transient failures
- **Resource Usage**: Minimal memory and CPU overhead
- **Caching**: Secret access should be cached to maintain P95 latency < 700ms
