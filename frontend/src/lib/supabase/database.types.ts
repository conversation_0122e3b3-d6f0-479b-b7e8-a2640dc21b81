export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  audit: {
    Tables: {
      log: {
        Row: {
          application_name: string | null
          backend_type: string | null
          client_addr: unknown | null
          command_tag: string | null
          context: string | null
          database_name: string | null
          detail: string | null
          error_severity: string | null
          hint: string | null
          id: number
          internal_query: string | null
          internal_query_pos: number | null
          location: string | null
          log_time: string
          message: string | null
          process_id: number | null
          query: string | null
          query_id: number | null
          query_pos: number | null
          session_id: string | null
          session_line_num: number | null
          session_start_time: string | null
          sql_state_code: string | null
          transaction_id: number | null
          user_name: string | null
          virtual_transaction_id: string | null
        }
        Insert: {
          application_name?: string | null
          backend_type?: string | null
          client_addr?: unknown | null
          command_tag?: string | null
          context?: string | null
          database_name?: string | null
          detail?: string | null
          error_severity?: string | null
          hint?: string | null
          id?: number
          internal_query?: string | null
          internal_query_pos?: number | null
          location?: string | null
          log_time?: string
          message?: string | null
          process_id?: number | null
          query?: string | null
          query_id?: number | null
          query_pos?: number | null
          session_id?: string | null
          session_line_num?: number | null
          session_start_time?: string | null
          sql_state_code?: string | null
          transaction_id?: number | null
          user_name?: string | null
          virtual_transaction_id?: string | null
        }
        Update: {
          application_name?: string | null
          backend_type?: string | null
          client_addr?: unknown | null
          command_tag?: string | null
          context?: string | null
          database_name?: string | null
          detail?: string | null
          error_severity?: string | null
          hint?: string | null
          id?: number
          internal_query?: string | null
          internal_query_pos?: number | null
          location?: string | null
          log_time?: string
          message?: string | null
          process_id?: number | null
          query?: string | null
          query_id?: number | null
          query_pos?: number | null
          session_id?: string | null
          session_line_num?: number | null
          session_start_time?: string | null
          sql_state_code?: string | null
          transaction_id?: number | null
          user_name?: string | null
          virtual_transaction_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      purge_old_logs: {
        Args: { retention_days?: number }
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  extensions: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      pg_stat_statements: {
        Row: {
          blk_read_time: number | null
          blk_write_time: number | null
          calls: number | null
          dbid: unknown | null
          jit_emission_count: number | null
          jit_emission_time: number | null
          jit_functions: number | null
          jit_generation_time: number | null
          jit_inlining_count: number | null
          jit_inlining_time: number | null
          jit_optimization_count: number | null
          jit_optimization_time: number | null
          local_blks_dirtied: number | null
          local_blks_hit: number | null
          local_blks_read: number | null
          local_blks_written: number | null
          max_exec_time: number | null
          max_plan_time: number | null
          mean_exec_time: number | null
          mean_plan_time: number | null
          min_exec_time: number | null
          min_plan_time: number | null
          plans: number | null
          query: string | null
          queryid: number | null
          rows: number | null
          shared_blks_dirtied: number | null
          shared_blks_hit: number | null
          shared_blks_read: number | null
          shared_blks_written: number | null
          stddev_exec_time: number | null
          stddev_plan_time: number | null
          temp_blk_read_time: number | null
          temp_blk_write_time: number | null
          temp_blks_read: number | null
          temp_blks_written: number | null
          toplevel: boolean | null
          total_exec_time: number | null
          total_plan_time: number | null
          userid: unknown | null
          wal_bytes: number | null
          wal_fpi: number | null
          wal_records: number | null
        }
        Relationships: []
      }
      pg_stat_statements_info: {
        Row: {
          dealloc: number | null
          stats_reset: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      algorithm_sign: {
        Args: { signables: string; secret: string; algorithm: string }
        Returns: string
      }
      armor: {
        Args: { "": string }
        Returns: string
      }
      dearmor: {
        Args: { "": string }
        Returns: string
      }
      gen_random_bytes: {
        Args: { "": number }
        Returns: string
      }
      gen_random_uuid: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      gen_salt: {
        Args: { "": string }
        Returns: string
      }
      pg_stat_statements: {
        Args: { showtext: boolean }
        Returns: Record<string, unknown>[]
      }
      pg_stat_statements_info: {
        Args: Record<PropertyKey, never>
        Returns: Record<string, unknown>
      }
      pg_stat_statements_reset: {
        Args: { userid?: unknown; dbid?: unknown; queryid?: number }
        Returns: undefined
      }
      pgp_armor_headers: {
        Args: { "": string }
        Returns: Record<string, unknown>[]
      }
      pgp_key_id: {
        Args: { "": string }
        Returns: string
      }
      sign: {
        Args: { payload: Json; secret: string; algorithm?: string }
        Returns: string
      }
      try_cast_double: {
        Args: { inp: string }
        Returns: number
      }
      url_decode: {
        Args: { data: string }
        Returns: string
      }
      url_encode: {
        Args: { data: string }
        Returns: string
      }
      uuid_generate_v1: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v1mc: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v3: {
        Args: { namespace: string; name: string }
        Returns: string
      }
      uuid_generate_v4: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v5: {
        Args: { namespace: string; name: string }
        Returns: string
      }
      uuid_nil: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_dns: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_oid: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_url: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_x500: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      verify: {
        Args: { token: string; secret: string; algorithm?: string }
        Returns: {
          header: Json
          payload: Json
          valid: boolean
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  graphql: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      _internal_resolve: {
        Args: {
          query: string
          variables?: Json
          operationName?: string
          extensions?: Json
        }
        Returns: Json
      }
      comment_directive: {
        Args: { comment_: string }
        Returns: Json
      }
      exception: {
        Args: { message: string }
        Returns: string
      }
      get_schema_version: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      resolve: {
        Args: {
          query: string
          variables?: Json
          operationName?: string
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  pgsodium_masks: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      auth_debug_logs: {
        Row: {
          connection_info: Json
          created_at: string | null
          id: number
        }
        Insert: {
          connection_info: Json
          created_at?: string | null
          id?: number
        }
        Update: {
          connection_info?: Json
          created_at?: string | null
          id?: number
        }
        Relationships: []
      }
      case_processing_batches: {
        Row: {
          created_at: string | null
          end_time: string | null
          error_message: string | null
          failure_count: number | null
          id: string
          jurisdiction: string
          query_params: Json | null
          skipped_count: number | null
          source: string
          start_time: string
          status: string | null
          success_count: number | null
          tenant_id: string | null
          total_cases: number | null
          updated_at: string | null
          user_id: string | null
          user_role: string | null
        }
        Insert: {
          created_at?: string | null
          end_time?: string | null
          error_message?: string | null
          failure_count?: number | null
          id: string
          jurisdiction: string
          query_params?: Json | null
          skipped_count?: number | null
          source: string
          start_time: string
          status?: string | null
          success_count?: number | null
          tenant_id?: string | null
          total_cases?: number | null
          updated_at?: string | null
          user_id?: string | null
          user_role?: string | null
        }
        Update: {
          created_at?: string | null
          end_time?: string | null
          error_message?: string | null
          failure_count?: number | null
          id?: string
          jurisdiction?: string
          query_params?: Json | null
          skipped_count?: number | null
          source?: string
          start_time?: string
          status?: string | null
          success_count?: number | null
          tenant_id?: string | null
          total_cases?: number | null
          updated_at?: string | null
          user_id?: string | null
          user_role?: string | null
        }
        Relationships: []
      }
      cases: {
        Row: {
          case_name: string
          case_name_full: string | null
          citation: string[] | null
          citation_count: number | null
          cluster_id: string | null
          completeness_score: number | null
          court_id: string | null
          created_at: string | null
          date_filed: string | null
          docket_id: string | null
          docket_number: string | null
          document_quality: string | null
          gcs_path: string | null
          id: string
          jurisdiction: string
          metadata_quality: string | null
          nature: string | null
          opinion_count: number | null
          pinecone_id: string | null
          precedential: boolean | null
          source: string | null
          source_id: string | null
          status: string | null
          tenant_id: string | null
          updated_at: string | null
          user_id: string | null
          user_role: string | null
        }
        Insert: {
          case_name: string
          case_name_full?: string | null
          citation?: string[] | null
          citation_count?: number | null
          cluster_id?: string | null
          completeness_score?: number | null
          court_id?: string | null
          created_at?: string | null
          date_filed?: string | null
          docket_id?: string | null
          docket_number?: string | null
          document_quality?: string | null
          gcs_path?: string | null
          id: string
          jurisdiction: string
          metadata_quality?: string | null
          nature?: string | null
          opinion_count?: number | null
          pinecone_id?: string | null
          precedential?: boolean | null
          source?: string | null
          source_id?: string | null
          status?: string | null
          tenant_id?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_role?: string | null
        }
        Update: {
          case_name?: string
          case_name_full?: string | null
          citation?: string[] | null
          citation_count?: number | null
          cluster_id?: string | null
          completeness_score?: number | null
          court_id?: string | null
          created_at?: string | null
          date_filed?: string | null
          docket_id?: string | null
          docket_number?: string | null
          document_quality?: string | null
          gcs_path?: string | null
          id?: string
          jurisdiction?: string
          metadata_quality?: string | null
          nature?: string | null
          opinion_count?: number | null
          pinecone_id?: string | null
          precedential?: boolean | null
          source?: string | null
          source_id?: string | null
          status?: string | null
          tenant_id?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_role?: string | null
        }
        Relationships: []
      }
      chunks: {
        Row: {
          case_citation: string | null
          case_date: string | null
          case_parties: string | null
          chapter: string | null
          chunk_index: number | null
          citation: string | null
          content: string | null
          created_at: string
          doc_type: string | null
          document_id: string | null
          document_name: string | null
          document_title: string | null
          gcs_url: string | null
          id: number
          jurisdiction: string | null
          namespace: string | null
          next_chunk_id: string | null
          page_numbers: number[] | null
          page_range: string | null
          pinecone_id: string | null
          practice_area: string | null
          prev_chunk_id: string | null
          section: string | null
          statute_section: string | null
          total_pages: number | null
        }
        Insert: {
          case_citation?: string | null
          case_date?: string | null
          case_parties?: string | null
          chapter?: string | null
          chunk_index?: number | null
          citation?: string | null
          content?: string | null
          created_at?: string
          doc_type?: string | null
          document_id?: string | null
          document_name?: string | null
          document_title?: string | null
          gcs_url?: string | null
          id?: number
          jurisdiction?: string | null
          namespace?: string | null
          next_chunk_id?: string | null
          page_numbers?: number[] | null
          page_range?: string | null
          pinecone_id?: string | null
          practice_area?: string | null
          prev_chunk_id?: string | null
          section?: string | null
          statute_section?: string | null
          total_pages?: number | null
        }
        Update: {
          case_citation?: string | null
          case_date?: string | null
          case_parties?: string | null
          chapter?: string | null
          chunk_index?: number | null
          citation?: string | null
          content?: string | null
          created_at?: string
          doc_type?: string | null
          document_id?: string | null
          document_name?: string | null
          document_title?: string | null
          gcs_url?: string | null
          id?: number
          jurisdiction?: string | null
          namespace?: string | null
          next_chunk_id?: string | null
          page_numbers?: number[] | null
          page_range?: string | null
          pinecone_id?: string | null
          practice_area?: string | null
          prev_chunk_id?: string | null
          section?: string | null
          statute_section?: string | null
          total_pages?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_document"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["document_id"]
          },
        ]
      }
      citation_patterns: {
        Row: {
          created_at: string
          description: string | null
          id: string
          jurisdiction_code: string
          pattern: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          jurisdiction_code: string
          pattern: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          jurisdiction_code?: string
          pattern?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_citation_patterns_jurisdiction"
            columns: ["jurisdiction_code"]
            isOneToOne: false
            referencedRelation: "jurisdictions"
            referencedColumns: ["code"]
          },
        ]
      }
      citations: {
        Row: {
          citation_text: string | null
          cited_case_id: string | null
          citing_case_id: string | null
          confidence: number | null
          created_at: string | null
          id: string
          neo4j_relationship_id: string | null
        }
        Insert: {
          citation_text?: string | null
          cited_case_id?: string | null
          citing_case_id?: string | null
          confidence?: number | null
          created_at?: string | null
          id: string
          neo4j_relationship_id?: string | null
        }
        Update: {
          citation_text?: string | null
          cited_case_id?: string | null
          citing_case_id?: string | null
          confidence?: number | null
          created_at?: string | null
          id?: string
          neo4j_relationship_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "citations_citing_case_id_fkey"
            columns: ["citing_case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
        ]
      }
      court_systems: {
        Row: {
          abbreviation: string | null
          created_at: string | null
          id: number
          jurisdiction_code: string
          level: string | null
          name: string
          updated_at: string | null
        }
        Insert: {
          abbreviation?: string | null
          created_at?: string | null
          id?: number
          jurisdiction_code: string
          level?: string | null
          name: string
          updated_at?: string | null
        }
        Update: {
          abbreviation?: string | null
          created_at?: string | null
          id?: number
          jurisdiction_code?: string
          level?: string | null
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_court_systems_jurisdiction"
            columns: ["jurisdiction_code"]
            isOneToOne: false
            referencedRelation: "jurisdictions"
            referencedColumns: ["code"]
          },
        ]
      }
      debug_jwt_logs: {
        Row: {
          claims: Json | null
          created_at: string | null
          id: number
        }
        Insert: {
          claims?: Json | null
          created_at?: string | null
          id?: number
        }
        Update: {
          claims?: Json | null
          created_at?: string | null
          id?: number
        }
        Relationships: []
      }
      documents: {
        Row: {
          created_at: string
          doc_type: string | null
          document_id: string | null
          gcs_path: string | null
          id: number
          jurisdiction: string | null
          namespace: string | null
          practice_area: string | null
          title: string | null
        }
        Insert: {
          created_at?: string
          doc_type?: string | null
          document_id?: string | null
          gcs_path?: string | null
          id?: number
          jurisdiction?: string | null
          namespace?: string | null
          practice_area?: string | null
          title?: string | null
        }
        Update: {
          created_at?: string
          doc_type?: string | null
          document_id?: string | null
          gcs_path?: string | null
          id?: number
          jurisdiction?: string | null
          namespace?: string | null
          practice_area?: string | null
          title?: string | null
        }
        Relationships: []
      }
      jurisdictions: {
        Row: {
          code: string
          created_at: string
          description: string | null
          name: string
        }
        Insert: {
          code: string
          created_at?: string
          description?: string | null
          name: string
        }
        Update: {
          code?: string
          created_at?: string
          description?: string | null
          name?: string
        }
        Relationships: []
      }
      legal_templates: {
        Row: {
          allows_conditional_blocks: boolean | null
          allows_loops: boolean | null
          category: string
          content: Json
          created_at: string | null
          created_by: string | null
          description: string | null
          document_type: string
          id: string
          is_active: boolean | null
          jurisdiction: string | null
          name: string
          practice_area: string
          state: string
          subcategory: string | null
          template_engine: string | null
          tenant_id: string
          updated_at: string | null
          variables: Json
          version: number
        }
        Insert: {
          allows_conditional_blocks?: boolean | null
          allows_loops?: boolean | null
          category: string
          content: Json
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          document_type: string
          id?: string
          is_active?: boolean | null
          jurisdiction?: string | null
          name: string
          practice_area: string
          state: string
          subcategory?: string | null
          template_engine?: string | null
          tenant_id: string
          updated_at?: string | null
          variables: Json
          version?: number
        }
        Update: {
          allows_conditional_blocks?: boolean | null
          allows_loops?: boolean | null
          category?: string
          content?: Json
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          document_type?: string
          id?: string
          is_active?: boolean | null
          jurisdiction?: string | null
          name?: string
          practice_area?: string
          state?: string
          subcategory?: string | null
          template_engine?: string | null
          tenant_id?: string
          updated_at?: string | null
          variables?: Json
          version?: number
        }
        Relationships: []
      }
      opinions: {
        Row: {
          author: string | null
          case_id: string | null
          created_at: string | null
          gcs_path: string | null
          has_text: boolean | null
          id: string
          opinion_type: string | null
          pinecone_id: string | null
          updated_at: string | null
          word_count: number | null
        }
        Insert: {
          author?: string | null
          case_id?: string | null
          created_at?: string | null
          gcs_path?: string | null
          has_text?: boolean | null
          id: string
          opinion_type?: string | null
          pinecone_id?: string | null
          updated_at?: string | null
          word_count?: number | null
        }
        Update: {
          author?: string | null
          case_id?: string | null
          created_at?: string | null
          gcs_path?: string | null
          has_text?: boolean | null
          id?: string
          opinion_type?: string | null
          pinecone_id?: string | null
          updated_at?: string | null
          word_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "opinions_case_id_fkey"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
        ]
      }
      processing_history: {
        Row: {
          action: string
          batch_id: string | null
          case_id: string
          created_at: string | null
          details: Json | null
          id: string
          status: string
          tenant_id: string | null
          user_id: string | null
          user_role: string | null
        }
        Insert: {
          action: string
          batch_id?: string | null
          case_id: string
          created_at?: string | null
          details?: Json | null
          id: string
          status: string
          tenant_id?: string | null
          user_id?: string | null
          user_role?: string | null
        }
        Update: {
          action?: string
          batch_id?: string | null
          case_id?: string
          created_at?: string | null
          details?: Json | null
          id?: string
          status?: string
          tenant_id?: string | null
          user_id?: string | null
          user_role?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "processing_history_batch_id_fkey"
            columns: ["batch_id"]
            isOneToOne: false
            referencedRelation: "case_processing_batches"
            referencedColumns: ["id"]
          },
        ]
      }
      security_events: {
        Row: {
          created_at: string | null
          details: Json | null
          event_type: string
          id: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          event_type: string
          id?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          event_type?: string
          id?: string
          user_id?: string | null
        }
        Relationships: []
      }
      template_categories: {
        Row: {
          created_at: string | null
          id: string
          level: number
          name: string
          parent_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          level: number
          name: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          level?: number
          name?: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "template_categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "template_categories"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      binary_quantize: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      block_device: {
        Args: { p_fingerprint: string }
        Returns: boolean
      }
      check_cross_system_case: {
        Args: { case_id: string }
        Returns: Json
      }
      check_notification_schedule_exists: {
        Args: {
          p_tenant_id: string
          p_user_id: string
          p_type: string
          p_scheduled_for_start: string
          p_scheduled_for_end: string
        }
        Returns: boolean
      }
      check_search_path: {
        Args: Record<PropertyKey, never>
        Returns: {
          role_name: string
          search_path: string
        }[]
      }
      check_tenant_feature_access: {
        Args: { p_tenant_id: string; p_feature_key: string }
        Returns: boolean
      }
      debug_auth_connection: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      debug_jwt_claims: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      exec_sql: {
        Args: { sql_query: string }
        Returns: Json
      }
      extend_subscription_trial: {
        Args: { p_subscription_id: string; p_days: number }
        Returns: boolean
      }
      function_exists: {
        Args: { p_function_name: string }
        Returns: boolean
      }
      generate_gcs_path: {
        Args: {
          tenant_id: string
          case_id?: string
          client_id?: string
          practice_area?: string
          document_category?: string
          subcategory?: string
          is_public?: boolean
          jurisdiction?: string
        }
        Returns: string
      }
      get_addon_popularity: {
        Args: Record<PropertyKey, never>
        Returns: {
          addon_name: string
          addon_code: string
          category: string
          attach_rate: number
          subscription_count: number
          total_subscriptions: number
        }[]
      }
      get_court_hierarchy: {
        Args: { jurisdiction_code: string }
        Returns: Json
      }
      get_courts: {
        Args: { jurisdiction_code: string }
        Returns: Json
      }
      get_jurisdiction_citation_patterns: {
        Args: { p_jurisdiction_code: string }
        Returns: Json
      }
      get_revenue_by_country: {
        Args: Record<PropertyKey, never>
        Returns: {
          country: string
          subscription_count: number
          monthly_revenue: number
          percentage: number
        }[]
      }
      get_revenue_by_state: {
        Args: { p_country: string }
        Returns: {
          state: string
          subscription_count: number
          monthly_revenue: number
          percentage: number
        }[]
      }
      get_subscription_addon_distribution: {
        Args: Record<PropertyKey, never>
        Returns: {
          addon_name: string
          addon_code: string
          category: string
          count: number
          percentage: number
          revenue: number
        }[]
      }
      get_subscription_churn_rate: {
        Args: { p_period?: string; p_limit?: number }
        Returns: {
          period: string
          churn_rate: number
          canceled_count: number
          total_count: number
        }[]
      }
      get_subscription_conversion_rate: {
        Args: { p_period?: string; p_limit?: number }
        Returns: {
          period: string
          trial_count: number
          converted_count: number
          conversion_rate: number
        }[]
      }
      get_subscription_geographic_distribution: {
        Args: Record<PropertyKey, never>
        Returns: {
          country: string
          state: string
          count: number
          percentage: number
        }[]
      }
      get_subscription_growth: {
        Args: { p_period?: string; p_limit?: number }
        Returns: {
          period: string
          new_subscriptions: number
          total_subscriptions: number
        }[]
      }
      get_subscription_plan_distribution: {
        Args: Record<PropertyKey, never>
        Returns: {
          plan_name: string
          plan_code: string
          count: number
          percentage: number
          revenue: number
        }[]
      }
      get_tenant_subscriptions_summary: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          tenant_id: string
          tenant_name: string
          plan_name: string
          plan_code: string
          status: string
          billing_cycle: string
          trial_end: string
          current_period_end: string
          addon_count: number
        }[]
      }
      get_user_devices: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          fingerprint: string
          first_seen: string
          last_seen: string
          ip_address: string
          user_agent: string
          device_name: string
          is_trusted: boolean
          is_blocked: boolean
        }[]
      }
      get_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      halfvec_avg: {
        Args: { "": number[] }
        Returns: unknown
      }
      halfvec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_send: {
        Args: { "": unknown }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      hnsw_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnswhandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      is_session_timed_out: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_trusted_device: {
        Args: { p_fingerprint: string }
        Returns: boolean
      }
      ivfflat_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflathandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      jurisdiction_exists: {
        Args: { p_code: string }
        Returns: boolean
      }
      l2_norm: {
        Args: { "": unknown } | { "": unknown }
        Returns: number
      }
      l2_normalize: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: unknown
      }
      name_device: {
        Args: { p_fingerprint: string; p_device_name: string }
        Returns: string
      }
      register_device: {
        Args: {
          p_fingerprint: string
          p_user_agent: string
          p_ip_address?: string
        }
        Returns: string
      }
      rename_device: {
        Args: { p_fingerprint: string; p_device_name: string }
        Returns: boolean
      }
      schedule_quota_approaching_notifications: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      schedule_trial_ending_notifications: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      setup_security_tables: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      sparsevec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      sparsevec_send: {
        Args: { "": unknown }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      trust_device: {
        Args: { p_fingerprint: string }
        Returns: boolean
      }
      upsert_tenant_user: {
        Args: {
          p_id: string
          p_email: string
          p_role: string
          p_tenant_id: string
          p_firm_role: string
        }
        Returns: Json
      }
      vector_avg: {
        Args: { "": number[] }
        Returns: string
      }
      vector_dims: {
        Args: { "": string } | { "": unknown }
        Returns: number
      }
      vector_norm: {
        Args: { "": string }
        Returns: number
      }
      vector_out: {
        Args: { "": string }
        Returns: unknown
      }
      vector_send: {
        Args: { "": string }
        Returns: string
      }
      vector_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
    }
    Enums: {
      case_status:
        | "pre_conflict_check"
        | "conflict_check_failed"
        | "accepted"
        | "active"
        | "closed"
        | "rejected"
        | "archived"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  realtime: {
    Tables: {
      messages: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      messages_2025_03_01: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      messages_2025_03_02: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      messages_2025_03_03: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      messages_2025_03_04: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      messages_2025_03_05: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      messages_2025_03_06: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      messages_2025_03_07: {
        Row: {
          event: string | null
          extension: string
          id: string
          inserted_at: string
          payload: Json | null
          private: boolean | null
          topic: string
          updated_at: string
        }
        Insert: {
          event?: string | null
          extension: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic: string
          updated_at?: string
        }
        Update: {
          event?: string | null
          extension?: string
          id?: string
          inserted_at?: string
          payload?: Json | null
          private?: boolean | null
          topic?: string
          updated_at?: string
        }
        Relationships: []
      }
      schema_migrations: {
        Row: {
          inserted_at: string | null
          version: number
        }
        Insert: {
          inserted_at?: string | null
          version: number
        }
        Update: {
          inserted_at?: string | null
          version?: number
        }
        Relationships: []
      }
      subscription: {
        Row: {
          claims: Json
          claims_role: unknown
          created_at: string
          entity: unknown
          filters: Database["realtime"]["CompositeTypes"]["user_defined_filter"][]
          id: number
          subscription_id: string
        }
        Insert: {
          claims: Json
          claims_role?: unknown
          created_at?: string
          entity: unknown
          filters?: Database["realtime"]["CompositeTypes"]["user_defined_filter"][]
          id?: never
          subscription_id: string
        }
        Update: {
          claims?: Json
          claims_role?: unknown
          created_at?: string
          entity?: unknown
          filters?: Database["realtime"]["CompositeTypes"]["user_defined_filter"][]
          id?: never
          subscription_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      apply_rls: {
        Args: { wal: Json; max_record_bytes?: number }
        Returns: Database["realtime"]["CompositeTypes"]["wal_rls"][]
      }
      broadcast_changes: {
        Args: {
          topic_name: string
          event_name: string
          operation: string
          table_name: string
          table_schema: string
          new: Record<string, unknown>
          old: Record<string, unknown>
          level?: string
        }
        Returns: undefined
      }
      build_prepared_statement_sql: {
        Args: {
          prepared_statement_name: string
          entity: unknown
          columns: Database["realtime"]["CompositeTypes"]["wal_column"][]
        }
        Returns: string
      }
      cast: {
        Args: { val: string; type_: unknown }
        Returns: Json
      }
      check_equality_op: {
        Args: {
          op: "eq" | "neq" | "lt" | "lte" | "gt" | "gte" | "in"
          type_: unknown
          val_1: string
          val_2: string
        }
        Returns: boolean
      }
      is_visible_through_filters: {
        Args: {
          columns: Database["realtime"]["CompositeTypes"]["wal_column"][]
          filters: Database["realtime"]["CompositeTypes"]["user_defined_filter"][]
        }
        Returns: boolean
      }
      list_changes: {
        Args: {
          publication: unknown
          slot_name: unknown
          max_changes: number
          max_record_bytes: number
        }
        Returns: Database["realtime"]["CompositeTypes"]["wal_rls"][]
      }
      quote_wal2json: {
        Args: { entity: unknown }
        Returns: string
      }
      send: {
        Args: { payload: Json; event: string; topic: string; private?: boolean }
        Returns: undefined
      }
      to_regrole: {
        Args: { role_name: string }
        Returns: unknown
      }
      topic: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
    }
    Enums: {
      action: "INSERT" | "UPDATE" | "DELETE" | "TRUNCATE" | "ERROR"
      equality_op: "eq" | "neq" | "lt" | "lte" | "gt" | "gte" | "in"
    }
    CompositeTypes: {
      user_defined_filter: {
        column_name: string | null
        op: Database["realtime"]["Enums"]["equality_op"] | null
        value: string | null
      }
      wal_column: {
        name: string | null
        type_name: string | null
        type_oid: unknown | null
        value: Json | null
        is_pkey: boolean | null
        is_selectable: boolean | null
      }
      wal_rls: {
        wal: Json | null
        is_rls_enabled: boolean | null
        subscription_ids: string[] | null
        errors: string[] | null
      }
    }
  }
  security: {
    Tables: {
      alert_configs: {
        Row: {
          created_at: string | null
          email: boolean | null
          id: string
          in_app: boolean | null
          min_severity: string | null
          sms: boolean | null
          tenant_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          email?: boolean | null
          id?: string
          in_app?: boolean | null
          min_severity?: string | null
          sms?: boolean | null
          tenant_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          email?: boolean | null
          id?: string
          in_app?: boolean | null
          min_severity?: string | null
          sms?: boolean | null
          tenant_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      alerts: {
        Row: {
          created_at: string | null
          details: Json | null
          event_id: string | null
          id: string
          message: string
          read: boolean | null
          read_at: string | null
          severity: string
          tenant_id: string | null
          title: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          event_id?: string | null
          id?: string
          message: string
          read?: boolean | null
          read_at?: string | null
          severity: string
          tenant_id?: string | null
          title: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          event_id?: string | null
          id?: string
          message?: string
          read?: boolean | null
          read_at?: string | null
          severity?: string
          tenant_id?: string | null
          title?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "alerts_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "dashboard_events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "alerts_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      anomalies: {
        Row: {
          created_at: string | null
          details: Json | null
          event_type: string
          id: string
          reasons: string[] | null
          related_event_id: string | null
          resolution_notes: string | null
          resolved_at: string | null
          resolved_by: string | null
          score: number
          severity: string
          status: string | null
          tenant_id: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          event_type: string
          id?: string
          reasons?: string[] | null
          related_event_id?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          score: number
          severity: string
          status?: string | null
          tenant_id?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          event_type?: string
          id?: string
          reasons?: string[] | null
          related_event_id?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          score?: number
          severity?: string
          status?: string | null
          tenant_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "anomalies_related_event_id_fkey"
            columns: ["related_event_id"]
            isOneToOne: false
            referencedRelation: "dashboard_events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "anomalies_related_event_id_fkey"
            columns: ["related_event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      auth_audit: {
        Row: {
          error_message: string | null
          event_time: string | null
          id: string
          ip_address: string | null
          jwt_claims: Json | null
          new_values: Json | null
          old_values: Json | null
          operation: string | null
          resource_id: string | null
          resource_type: string | null
          success: boolean | null
          tenant_id: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          error_message?: string | null
          event_time?: string | null
          id?: string
          ip_address?: string | null
          jwt_claims?: Json | null
          new_values?: Json | null
          old_values?: Json | null
          operation?: string | null
          resource_id?: string | null
          resource_type?: string | null
          success?: boolean | null
          tenant_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          error_message?: string | null
          event_time?: string | null
          id?: string
          ip_address?: string | null
          jwt_claims?: Json | null
          new_values?: Json | null
          old_values?: Json | null
          operation?: string | null
          resource_id?: string | null
          resource_type?: string | null
          success?: boolean | null
          tenant_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      device_fingerprints: {
        Row: {
          device_name: string | null
          fingerprint: string
          first_seen: string | null
          id: string
          ip_address: string | null
          is_blocked: boolean | null
          is_trusted: boolean | null
          last_seen: string | null
          tenant_id: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          device_name?: string | null
          fingerprint: string
          first_seen?: string | null
          id?: string
          ip_address?: string | null
          is_blocked?: boolean | null
          is_trusted?: boolean | null
          last_seen?: string | null
          tenant_id?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          device_name?: string | null
          fingerprint?: string
          first_seen?: string | null
          id?: string
          ip_address?: string | null
          is_blocked?: boolean | null
          is_trusted?: boolean | null
          last_seen?: string | null
          tenant_id?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: []
      }
      events: {
        Row: {
          created_at: string | null
          details: Json | null
          event_category: string | null
          event_type: string
          id: string
          ip_address: string | null
          location_city: string | null
          location_coordinates: unknown | null
          location_country: string | null
          location_region: string | null
          tenant_id: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          event_category?: string | null
          event_type: string
          id?: string
          ip_address?: string | null
          location_city?: string | null
          location_coordinates?: unknown | null
          location_country?: string | null
          location_region?: string | null
          tenant_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          event_category?: string | null
          event_type?: string
          id?: string
          ip_address?: string | null
          location_city?: string | null
          location_coordinates?: unknown | null
          location_country?: string | null
          location_region?: string | null
          tenant_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      geolocation_history: {
        Row: {
          city: string | null
          country: string | null
          created_at: string
          id: string
          ip_address: string
          latitude: number | null
          longitude: number | null
          region: string | null
          tenant_id: string
          user_id: string
        }
        Insert: {
          city?: string | null
          country?: string | null
          created_at?: string
          id?: string
          ip_address: string
          latitude?: number | null
          longitude?: number | null
          region?: string | null
          tenant_id: string
          user_id: string
        }
        Update: {
          city?: string | null
          country?: string | null
          created_at?: string
          id?: string
          ip_address?: string
          latitude?: number | null
          longitude?: number | null
          region?: string | null
          tenant_id?: string
          user_id?: string
        }
        Relationships: []
      }
      revoked_sessions: {
        Row: {
          jti: string
          reason: string | null
          revoked_at: string | null
          revoked_by: string | null
          tenant_id: string | null
          user_id: string | null
        }
        Insert: {
          jti: string
          reason?: string | null
          revoked_at?: string | null
          revoked_by?: string | null
          tenant_id?: string | null
          user_id?: string | null
        }
        Update: {
          jti?: string
          reason?: string | null
          revoked_at?: string | null
          revoked_by?: string | null
          tenant_id?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      tenant_settings: {
        Row: {
          allowed_countries: string[] | null
          allowed_ip_ranges: string[] | null
          anomaly_detection_level: string | null
          auto_block_suspicious_activity: boolean | null
          blocked_countries: string[] | null
          created_at: string | null
          id: string
          max_devices_per_user: number | null
          max_sessions_per_user: number | null
          password_expiry_days: number | null
          require_mfa: boolean | null
          security_contact_email: string | null
          security_contact_phone: string | null
          settings: Json | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          allowed_countries?: string[] | null
          allowed_ip_ranges?: string[] | null
          anomaly_detection_level?: string | null
          auto_block_suspicious_activity?: boolean | null
          blocked_countries?: string[] | null
          created_at?: string | null
          id?: string
          max_devices_per_user?: number | null
          max_sessions_per_user?: number | null
          password_expiry_days?: number | null
          require_mfa?: boolean | null
          security_contact_email?: string | null
          security_contact_phone?: string | null
          settings?: Json | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          allowed_countries?: string[] | null
          allowed_ip_ranges?: string[] | null
          anomaly_detection_level?: string | null
          auto_block_suspicious_activity?: boolean | null
          blocked_countries?: string[] | null
          created_at?: string | null
          id?: string
          max_devices_per_user?: number | null
          max_sessions_per_user?: number | null
          password_expiry_days?: number | null
          require_mfa?: boolean | null
          security_contact_email?: string | null
          security_contact_phone?: string | null
          settings?: Json | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      tenant_usage_metrics: {
        Row: {
          active_users_count: number | null
          alerts_count: number | null
          anomalies_count: number | null
          created_at: string | null
          details: Json | null
          failed_login_attempts_count: number | null
          id: string
          metric_date: string
          security_events_count: number | null
          tenant_id: string
          total_api_calls_count: number | null
          total_login_attempts_count: number | null
          total_sessions_count: number | null
          updated_at: string | null
        }
        Insert: {
          active_users_count?: number | null
          alerts_count?: number | null
          anomalies_count?: number | null
          created_at?: string | null
          details?: Json | null
          failed_login_attempts_count?: number | null
          id?: string
          metric_date: string
          security_events_count?: number | null
          tenant_id: string
          total_api_calls_count?: number | null
          total_login_attempts_count?: number | null
          total_sessions_count?: number | null
          updated_at?: string | null
        }
        Update: {
          active_users_count?: number | null
          alerts_count?: number | null
          anomalies_count?: number | null
          created_at?: string | null
          details?: Json | null
          failed_login_attempts_count?: number | null
          id?: string
          metric_date?: string
          security_events_count?: number | null
          tenant_id?: string
          total_api_calls_count?: number | null
          total_login_attempts_count?: number | null
          total_sessions_count?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      token_usage: {
        Row: {
          created_at: string | null
          expires_at: string | null
          id: string
          ip_addresses: string[] | null
          is_revoked: boolean | null
          issued_at: string
          last_used: string | null
          revocation_reason: string | null
          revoked_at: string | null
          revoked_by: string | null
          tenant_id: string | null
          token_id: string
          updated_at: string | null
          user_agents: string[] | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at?: string | null
          id?: string
          ip_addresses?: string[] | null
          is_revoked?: boolean | null
          issued_at: string
          last_used?: string | null
          revocation_reason?: string | null
          revoked_at?: string | null
          revoked_by?: string | null
          tenant_id?: string | null
          token_id: string
          updated_at?: string | null
          user_agents?: string[] | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string | null
          id?: string
          ip_addresses?: string[] | null
          is_revoked?: boolean | null
          issued_at?: string
          last_used?: string | null
          revocation_reason?: string | null
          revoked_at?: string | null
          revoked_by?: string | null
          tenant_id?: string | null
          token_id?: string
          updated_at?: string | null
          user_agents?: string[] | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      dashboard_events: {
        Row: {
          created_at: string | null
          details: Json | null
          event_type: string | null
          id: string | null
          ip_address: string | null
          user_agent: string | null
          user_email: string | null
          user_id: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      block_device: {
        Args: { p_fingerprint: string }
        Returns: boolean
      }
      create_action_signature: {
        Args: { p_action: string; p_data: Json }
        Returns: string
      }
      create_alert: {
        Args: {
          p_user_id: string
          p_tenant_id: string
          p_title: string
          p_message: string
          p_severity: string
          p_event_id?: string
          p_details?: Json
        }
        Returns: string
      }
      detect_anomalies: {
        Args: {
          p_user_id: string
          p_tenant_id: string
          p_ip_address: string
          p_user_agent: string
          p_event_type: string
          p_event_id: string
        }
        Returns: string
      }
      get_user_devices: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          fingerprint: string
          first_seen: string
          last_seen: string
          ip_address: string
          user_agent: string
          device_name: string
          is_trusted: boolean
          is_blocked: boolean
        }[]
      }
      is_session_revoked: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_session_timed_out: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_trusted_device: {
        Args: { p_fingerprint: string }
        Returns: boolean
      }
      log_auth_event: {
        Args: {
          operation: string
          resource_type?: string
          resource_id?: string
          old_values?: Json
          new_values?: Json
          success?: boolean
          error_message?: string
        }
        Returns: string
      }
      log_event: {
        Args:
          | { p_event_type: string; p_details?: Json }
          | {
              p_event_type: string
              p_user_id: string
              p_tenant_id: string
              p_ip_address?: string
              p_user_agent?: string
              p_event_category?: string
              p_details?: Json
            }
        Returns: string
      }
      name_device: {
        Args: { p_fingerprint: string; p_device_name: string }
        Returns: string
      }
      purge_old_events: {
        Args: { retention_days?: number }
        Returns: number
      }
      record_token_usage: {
        Args: {
          p_token_id: string
          p_user_id: string
          p_tenant_id: string
          p_ip_address: string
          p_user_agent: string
        }
        Returns: undefined
      }
      register_device: {
        Args: {
          p_user_id: string
          p_tenant_id: string
          p_fingerprint: string
          p_user_agent?: string
          p_ip_address?: string
        }
        Returns: string
      }
      rename_device: {
        Args: { p_fingerprint: string; p_device_name: string }
        Returns: boolean
      }
      revoke_all_user_sessions: {
        Args: { target_user_id: string; revocation_reason: string }
        Returns: number
      }
      revoke_session: {
        Args: { session_jti: string; revocation_reason: string }
        Returns: boolean
      }
      trust_device: {
        Args: { p_fingerprint: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_insert_object: {
        Args: { bucketid: string; name: string; owner: string; metadata: Json }
        Returns: undefined
      }
      extension: {
        Args: { name: string }
        Returns: string
      }
      filename: {
        Args: { name: string }
        Returns: string
      }
      foldername: {
        Args: { name: string }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  tenants: {
    Tables: {
      insights: {
        Row: {
          id: string
          user_id: string
          matter_id: string | null
          title: string
          description: string | null
          category: "risk" | "task" | "calendar" | "efficiency" | "coaching"
          priority: number
          confidence: number
          suggested_action: Json | null
          source: string
          status: "new" | "snoozed" | "resolved"
          created_at: string
          expires_at: string | null
          tenant_id: string
        }
        Insert: {
          id?: string
          user_id: string
          matter_id?: string | null
          title: string
          description?: string | null
          category: "risk" | "task" | "calendar" | "efficiency" | "coaching"
          priority: number
          confidence: number
          suggested_action?: Json | null
          source: string
          status?: "new" | "snoozed" | "resolved"
          created_at?: string
          expires_at?: string | null
          tenant_id: string
        }
        Update: {
          id?: string
          user_id?: string
          matter_id?: string | null
          title?: string
          description?: string | null
          category?: "risk" | "task" | "calendar" | "efficiency" | "coaching"
          priority?: number
          confidence?: number
          suggested_action?: Json | null
          source?: string
          status?: "new" | "snoozed" | "resolved"
          created_at?: string
          expires_at?: string | null
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "insights_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "insights_matter_id_fkey"
            columns: ["matter_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          }
        ]
      }
      insight_templates: {
        Row: {
          id: string
          name: string
          category: "risk" | "task" | "calendar" | "efficiency" | "coaching"
          prompt: string
          scoring_logic: string | null
          enabled: boolean
          tenant_id: string
        }
        Insert: {
          id?: string
          name: string
          category: "risk" | "task" | "calendar" | "efficiency" | "coaching"
          prompt: string
          scoring_logic?: string | null
          enabled?: boolean
          tenant_id: string
        }
        Update: {
          id?: string
          name?: string
          category?: "risk" | "task" | "calendar" | "efficiency" | "coaching"
          prompt?: string
          scoring_logic?: string | null
          enabled?: boolean
          tenant_id?: string
        }
        Relationships: []
      }
      insight_feedback: {
        Row: {
          id: string
          insight_id: string
          user_id: string
          is_helpful: boolean
          feedback_text: string | null
          created_at: string
          tenant_id: string
        }
        Insert: {
          id?: string
          insight_id: string
          user_id: string
          is_helpful: boolean
          feedback_text?: string | null
          created_at?: string
          tenant_id: string
        }
        Update: {
          id?: string
          insight_id?: string
          user_id?: string
          is_helpful?: boolean
          feedback_text?: string | null
          created_at?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "insight_feedback_insight_id_fkey"
            columns: ["insight_id"]
            isOneToOne: false
            referencedRelation: "insights"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "insight_feedback_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      access_logs: {
        Row: {
          action: string
          id: string
          metadata: Json | null
          resource_id: string
          resource_type: string
          tenant_id: string
          timestamp: string | null
          user_id: string
        }
        Insert: {
          action: string
          id?: string
          metadata?: Json | null
          resource_id: string
          resource_type: string
          tenant_id: string
          timestamp?: string | null
          user_id: string
        }
        Update: {
          action?: string
          id?: string
          metadata?: Json | null
          resource_id?: string
          resource_type?: string
          tenant_id?: string
          timestamp?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "access_logs_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
          {
            foreignKeyName: "access_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      activities: {
        Row: {
          created_at: string | null
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          tenant_id: string
          type: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          tenant_id: string
          type: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          tenant_id?: string
          type?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      activity_feedback: {
        Row: {
          actions_taken: Json | null
          activity_id: string
          created_at: string | null
          feedback: string | null
          id: string
          priority_score: number | null
          rating: number | null
          tenant_id: string
          time_spent_seconds: number | null
          user_id: string
        }
        Insert: {
          actions_taken?: Json | null
          activity_id: string
          created_at?: string | null
          feedback?: string | null
          id?: string
          priority_score?: number | null
          rating?: number | null
          tenant_id: string
          time_spent_seconds?: number | null
          user_id: string
        }
        Update: {
          actions_taken?: Json | null
          activity_id?: string
          created_at?: string | null
          feedback?: string | null
          id?: string
          priority_score?: number | null
          rating?: number | null
          tenant_id?: string
          time_spent_seconds?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_feedback_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_feedback_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      assignments: {
        Row: {
          assigned_at: string | null
          assigned_by: string
          case_id: string
          id: string
          role: string
          tenant_id: string
          user_id: string
        }
        Insert: {
          assigned_at?: string | null
          assigned_by: string
          case_id: string
          id?: string
          role: string
          tenant_id: string
          user_id: string
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string
          case_id?: string
          id?: string
          role?: string
          tenant_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "assignments_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assignments_case_id_fkey"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assignments_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
          {
            foreignKeyName: "assignments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      authored_document_approvals: {
        Row: {
          approver_id: string
          comments: string | null
          document_id: string
          id: string
          metadata: Json | null
          reviewed_at: string | null
          status: string
          tenant_id: string
        }
        Insert: {
          approver_id: string
          comments?: string | null
          document_id: string
          id?: string
          metadata?: Json | null
          reviewed_at?: string | null
          status?: string
          tenant_id: string
        }
        Update: {
          approver_id?: string
          comments?: string | null
          document_id?: string
          id?: string
          metadata?: Json | null
          reviewed_at?: string | null
          status?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_approvals_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "authored_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_approvals_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      authored_document_collaborators: {
        Row: {
          cursor_position: Json | null
          document_id: string
          id: string
          last_active: string | null
          selection_range: Json | null
          tenant_id: string
          user_id: string
        }
        Insert: {
          cursor_position?: Json | null
          document_id: string
          id?: string
          last_active?: string | null
          selection_range?: Json | null
          tenant_id: string
          user_id: string
        }
        Update: {
          cursor_position?: Json | null
          document_id?: string
          id?: string
          last_active?: string | null
          selection_range?: Json | null
          tenant_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_collaboration_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "authored_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_collaboration_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      authored_document_comments: {
        Row: {
          author_id: string
          created_at: string | null
          document_id: string
          id: string
          metadata: Json | null
          position: Json | null
          resolved: boolean | null
          resolved_at: string | null
          resolved_by: string | null
          tenant_id: string
          text: string
          updated_at: string | null
        }
        Insert: {
          author_id: string
          created_at?: string | null
          document_id: string
          id?: string
          metadata?: Json | null
          position?: Json | null
          resolved?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          tenant_id: string
          text: string
          updated_at?: string | null
        }
        Update: {
          author_id?: string
          created_at?: string | null
          document_id?: string
          id?: string
          metadata?: Json | null
          position?: Json | null
          resolved?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          tenant_id?: string
          text?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_comments_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "authored_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_comments_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      authored_document_editors: {
        Row: {
          added_at: string | null
          added_by: string
          document_id: string
          editor_id: string
          id: string
          permission_level: string
          tenant_id: string
        }
        Insert: {
          added_at?: string | null
          added_by: string
          document_id: string
          editor_id: string
          id?: string
          permission_level: string
          tenant_id: string
        }
        Update: {
          added_at?: string | null
          added_by?: string
          document_id?: string
          editor_id?: string
          id?: string
          permission_level?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_editors_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "authored_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_editors_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      authored_document_signatures: {
        Row: {
          created_at: string | null
          document_id: string
          id: string
          ip_address: string | null
          metadata: Json | null
          signature_data: string | null
          signed_at: string | null
          signer_email: string
          signer_id: string
          status: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          document_id: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          signature_data?: string | null
          signed_at?: string | null
          signer_email: string
          signer_id: string
          status?: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          document_id?: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          signature_data?: string | null
          signed_at?: string | null
          signer_email?: string
          signer_id?: string
          status?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_signatures_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "authored_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_signatures_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      authored_document_templates: {
        Row: {
          category: string
          content: string
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          metadata: Json | null
          tenant_id: string
          title: string
          updated_at: string | null
          updated_by: string | null
          variables: Json | null
          version: number | null
        }
        Insert: {
          category: string
          content: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          tenant_id: string
          title: string
          updated_at?: string | null
          updated_by?: string | null
          variables?: Json | null
          version?: number | null
        }
        Update: {
          category?: string
          content?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          tenant_id?: string
          title?: string
          updated_at?: string | null
          updated_by?: string | null
          variables?: Json | null
          version?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "document_templates_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      authored_documents: {
        Row: {
          case_id: string | null
          client_id: string | null
          content: string
          created_at: string | null
          created_by: string | null
          embedding_status: string | null
          gcs_path: string | null
          id: string
          last_embedded_at: string | null
          metadata: Json | null
          sent_at: string | null
          signed_at: string | null
          status: string
          template_id: string | null
          tenant_id: string
          title: string
          updated_at: string | null
          updated_by: string | null
          variables_used: Json | null
          version: number | null
        }
        Insert: {
          case_id?: string | null
          client_id?: string | null
          content: string
          created_at?: string | null
          created_by?: string | null
          embedding_status?: string | null
          gcs_path?: string | null
          id?: string
          last_embedded_at?: string | null
          metadata?: Json | null
          sent_at?: string | null
          signed_at?: string | null
          status?: string
          template_id?: string | null
          tenant_id: string
          title: string
          updated_at?: string | null
          updated_by?: string | null
          variables_used?: Json | null
          version?: number | null
        }
        Update: {
          case_id?: string | null
          client_id?: string | null
          content?: string
          created_at?: string | null
          created_by?: string | null
          embedding_status?: string | null
          gcs_path?: string | null
          id?: string
          last_embedded_at?: string | null
          metadata?: Json | null
          sent_at?: string | null
          signed_at?: string | null
          status?: string
          template_id?: string | null
          tenant_id?: string
          title?: string
          updated_at?: string | null
          updated_by?: string | null
          variables_used?: Json | null
          version?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "drafted_documents_case_id_fkey"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "drafted_documents_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "authored_document_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "drafted_documents_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      case_clients: {
        Row: {
          case_id: string
          client_id: string
          tenant_id: string
        }
        Insert: {
          case_id: string
          client_id: string
          tenant_id: string
        }
        Update: {
          case_id?: string
          client_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "case_clients_case_id_fkey"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_clients_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_clients_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      case_documents: {
        Row: {
          case_id: string | null
          citation: string | null
          created_at: string | null
          created_by: string | null
          document_category: string | null
          document_type: string
          gcs_path: string | null
          id: string
          metadata: Json | null
          practice_area: string | null
          sensitive: boolean | null
          subcategory: string | null
          tenant_id: string
          title: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          case_id?: string | null
          citation?: string | null
          created_at?: string | null
          created_by?: string | null
          document_category?: string | null
          document_type: string
          gcs_path?: string | null
          id?: string
          metadata?: Json | null
          practice_area?: string | null
          sensitive?: boolean | null
          subcategory?: string | null
          tenant_id: string
          title: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          case_id?: string | null
          citation?: string | null
          created_at?: string | null
          created_by?: string | null
          document_category?: string | null
          document_type?: string
          gcs_path?: string | null
          id?: string
          metadata?: Json | null
          practice_area?: string | null
          sensitive?: boolean | null
          subcategory?: string | null
          tenant_id?: string
          title?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "firm_documents_case_id_fkey"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "firm_documents_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "firm_documents_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      case_parties: {
        Row: {
          case_id: string
          party_id: string
          role: string
          tenant_id: string
        }
        Insert: {
          case_id: string
          party_id: string
          role: string
          tenant_id: string
        }
        Update: {
          case_id?: string
          party_id?: string
          role?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "case_parties_case_id_fkey"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_parties_party_id_fkey"
            columns: ["party_id"]
            isOneToOne: false
            referencedRelation: "parties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_parties_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      cases: {
        Row: {
          case_type: string | null
          client_id: string
          closed_date: string | null
          consulted_attorney: boolean | null
          created_at: string | null
          created_by: string | null
          date_of_incident: string | null
          description: string | null
          id: string
          intake_priority: string | null
          metadata: Json | null
          opened_date: string | null
          practice_area: string | null
          previously_consulted: boolean | null
          rejection_reason: string | null
          sensitive: boolean | null
          status: string
          tenant_id: string
          title: string
          updated_at: string | null
        }
        Insert: {
          case_type?: string | null
          client_id: string
          closed_date?: string | null
          consulted_attorney?: boolean | null
          created_at?: string | null
          created_by?: string | null
          date_of_incident?: string | null
          description?: string | null
          id?: string
          intake_priority?: string | null
          metadata?: Json | null
          opened_date?: string | null
          practice_area?: string | null
          previously_consulted?: boolean | null
          rejection_reason?: string | null
          sensitive?: boolean | null
          status: string
          tenant_id: string
          title: string
          updated_at?: string | null
        }
        Update: {
          case_type?: string | null
          client_id?: string
          closed_date?: string | null
          consulted_attorney?: boolean | null
          created_at?: string | null
          created_by?: string | null
          date_of_incident?: string | null
          description?: string | null
          id?: string
          intake_priority?: string | null
          metadata?: Json | null
          opened_date?: string | null
          practice_area?: string | null
          previously_consulted?: boolean | null
          rejection_reason?: string | null
          sensitive?: boolean | null
          status?: string
          tenant_id?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "cases_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cases_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cases_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      client_documents: {
        Row: {
          client_id: string
          created_at: string | null
          created_by: string | null
          document_category: string
          gcs_path: string
          id: string
          metadata: Json | null
          sensitive: boolean | null
          subcategory: string | null
          tenant_id: string
          title: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          client_id: string
          created_at?: string | null
          created_by?: string | null
          document_category: string
          gcs_path: string
          id?: string
          metadata?: Json | null
          sensitive?: boolean | null
          subcategory?: string | null
          tenant_id: string
          title: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          client_id?: string
          created_at?: string | null
          created_by?: string | null
          document_category?: string
          gcs_path?: string
          id?: string
          metadata?: Json | null
          sensitive?: boolean | null
          subcategory?: string | null
          tenant_id?: string
          title?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_documents_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      clients: {
        Row: {
          address: Json
          assigned_attorney_id: string | null
          business_name: string | null
          business_type: string | null
          client_type: string
          communication_preferences: Json | null
          conflict_check_notes: string | null
          conflict_check_status: string | null
          created_at: string | null
          created_by: string | null
          custom_fields: Json | null
          date_of_birth: string | null
          document_delivery_method: string | null
          email: string | null
          employer: string | null
          first_name: string
          id: string
          intake_date: string
          last_name: string
          metadata: Json | null
          middle_name: string | null
          notes: string | null
          occupation: string | null
          phone_primary: string | null
          phone_secondary: string | null
          preferred_contact_method: string | null
          referred_by: string | null
          ssn: string | null
          status: string
          tax_id: string | null
          tenant_id: string
          updated_at: string | null
          work_status: string | null
        }
        Insert: {
          address?: Json
          assigned_attorney_id?: string | null
          business_name?: string | null
          business_type?: string | null
          client_type: string
          communication_preferences?: Json | null
          conflict_check_notes?: string | null
          conflict_check_status?: string | null
          created_at?: string | null
          created_by?: string | null
          custom_fields?: Json | null
          date_of_birth?: string | null
          document_delivery_method?: string | null
          email?: string | null
          employer?: string | null
          first_name: string
          id?: string
          intake_date?: string
          last_name: string
          metadata?: Json | null
          middle_name?: string | null
          notes?: string | null
          occupation?: string | null
          phone_primary?: string | null
          phone_secondary?: string | null
          preferred_contact_method?: string | null
          referred_by?: string | null
          ssn?: string | null
          status?: string
          tax_id?: string | null
          tenant_id: string
          updated_at?: string | null
          work_status?: string | null
        }
        Update: {
          address?: Json
          assigned_attorney_id?: string | null
          business_name?: string | null
          business_type?: string | null
          client_type?: string
          communication_preferences?: Json | null
          conflict_check_notes?: string | null
          conflict_check_status?: string | null
          created_at?: string | null
          created_by?: string | null
          custom_fields?: Json | null
          date_of_birth?: string | null
          document_delivery_method?: string | null
          email?: string | null
          employer?: string | null
          first_name?: string
          id?: string
          intake_date?: string
          last_name?: string
          metadata?: Json | null
          middle_name?: string | null
          notes?: string | null
          occupation?: string | null
          phone_primary?: string | null
          phone_secondary?: string | null
          preferred_contact_method?: string | null
          referred_by?: string | null
          ssn?: string | null
          status?: string
          tax_id?: string | null
          tenant_id?: string
          updated_at?: string | null
          work_status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clients_assigned_attorney_id_fkey"
            columns: ["assigned_attorney_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clients_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clients_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      conflict_checks: {
        Row: {
          case_id: string | null
          conflict_type: string | null
          created_at: string | null
          description: string | null
          id: string
          party_id: string | null
          resolved_at: string | null
          status: string
          tenant_id: string | null
          updated_at: string | null
        }
        Insert: {
          case_id?: string | null
          conflict_type?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          party_id?: string | null
          resolved_at?: string | null
          status: string
          tenant_id?: string | null
          updated_at?: string | null
        }
        Update: {
          case_id?: string | null
          conflict_type?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          party_id?: string | null
          resolved_at?: string | null
          status?: string
          tenant_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conflict_checks_case_id_fkey"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conflict_checks_party_id_fkey"
            columns: ["party_id"]
            isOneToOne: false
            referencedRelation: "parties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conflict_checks_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      court_holidays: {
        Row: {
          created_at: string | null
          date: string
          id: string
          is_recurring: boolean | null
          jurisdiction_id: string
          name: string
          recurring_pattern: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          date: string
          id?: string
          is_recurring?: boolean | null
          jurisdiction_id: string
          name: string
          recurring_pattern?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          date?: string
          id?: string
          is_recurring?: boolean | null
          jurisdiction_id?: string
          name?: string
          recurring_pattern?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      deadlines: {
        Row: {
          assigned_to: string[] | null
          base_date: string | null
          calculation_notes: string | null
          case_id: string
          completed_at: string | null
          completed_by: string | null
          created_at: string | null
          created_by: string
          description: string | null
          due_date: string
          id: string
          metadata: Json | null
          priority: string | null
          rule_id: string | null
          status: string | null
          tenant_id: string
          title: string
          updated_at: string | null
        }
        Insert: {
          assigned_to?: string[] | null
          base_date?: string | null
          calculation_notes?: string | null
          case_id: string
          completed_at?: string | null
          completed_by?: string | null
          created_at?: string | null
          created_by: string
          description?: string | null
          due_date: string
          id?: string
          metadata?: Json | null
          priority?: string | null
          rule_id?: string | null
          status?: string | null
          tenant_id: string
          title: string
          updated_at?: string | null
        }
        Update: {
          assigned_to?: string[] | null
          base_date?: string | null
          calculation_notes?: string | null
          case_id?: string
          completed_at?: string | null
          completed_by?: string | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          due_date?: string
          id?: string
          metadata?: Json | null
          priority?: string | null
          rule_id?: string | null
          status?: string | null
          tenant_id?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      document_categories: {
        Row: {
          category_type: string
          created_at: string | null
          description: string | null
          id: string
          name: string
          parent_id: string | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          category_type: string
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          parent_id?: string | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          category_type?: string
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          parent_id?: string | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "document_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      document_chunks: {
        Row: {
          authored_document_id: string | null
          content: string
          created_at: string | null
          document_id: string
          embedding: string | null
          id: string
          metadata: Json | null
          source_type: string | null
          tenant_id: string
        }
        Insert: {
          authored_document_id?: string | null
          content: string
          created_at?: string | null
          document_id: string
          embedding?: string | null
          id?: string
          metadata?: Json | null
          source_type?: string | null
          tenant_id: string
        }
        Update: {
          authored_document_id?: string | null
          content?: string
          created_at?: string | null
          document_id?: string
          embedding?: string | null
          id?: string
          metadata?: Json | null
          source_type?: string | null
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_chunks_authored_document_id_fkey"
            columns: ["authored_document_id"]
            isOneToOne: false
            referencedRelation: "authored_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_chunks_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "case_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_chunks_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      document_highlights: {
        Row: {
          comment: string | null
          created_at: string
          created_by: string
          document_id: string
          document_type: string
          end_offset: number
          highlight_color: string | null
          id: string
          start_offset: number
          tenant_id: string
          updated_at: string
        }
        Insert: {
          comment?: string | null
          created_at?: string
          created_by: string
          document_id: string
          document_type: string
          end_offset: number
          highlight_color?: string | null
          id?: string
          start_offset: number
          tenant_id: string
          updated_at?: string
        }
        Update: {
          comment?: string | null
          created_at?: string
          created_by?: string
          document_id?: string
          document_type?: string
          end_offset?: number
          highlight_color?: string | null
          id?: string
          start_offset?: number
          tenant_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_highlights_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      document_history: {
        Row: {
          change_type: string
          changed_at: string | null
          changed_by: string | null
          content: string
          content_diff: Json | null
          document_id: string
          id: string
          metadata: Json | null
          parent_version: string | null
          parent_version_id: string | null
          tenant_id: string
          variables_used: Json | null
          version_number: number
        }
        Insert: {
          change_type: string
          changed_at?: string | null
          changed_by?: string | null
          content: string
          content_diff?: Json | null
          document_id: string
          id?: string
          metadata?: Json | null
          parent_version?: string | null
          parent_version_id?: string | null
          tenant_id: string
          variables_used?: Json | null
          version_number: number
        }
        Update: {
          change_type?: string
          changed_at?: string | null
          changed_by?: string | null
          content?: string
          content_diff?: Json | null
          document_id?: string
          id?: string
          metadata?: Json | null
          parent_version?: string | null
          parent_version_id?: string | null
          tenant_id?: string
          variables_used?: Json | null
          version_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "document_history_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "authored_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_history_parent_version_fkey"
            columns: ["parent_version"]
            isOneToOne: false
            referencedRelation: "document_history"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_history_parent_version_id_fkey"
            columns: ["parent_version_id"]
            isOneToOne: false
            referencedRelation: "document_history"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_history_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      document_summary_feedback: {
        Row: {
          created_at: string
          document_id: string
          document_type: string
          feedback: string | null
          id: string
          rating: number
          summary_id: string | null
          tenant_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          document_id: string
          document_type?: string
          feedback?: string | null
          id?: string
          rating: number
          summary_id?: string | null
          tenant_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          document_id?: string
          document_type?: string
          feedback?: string | null
          id?: string
          rating?: number
          summary_id?: string | null
          tenant_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_summary_feedback_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
          {
            foreignKeyName: "document_summary_feedback_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          all_day: boolean | null
          assigned_to: string[] | null
          case_id: string | null
          client_id: string | null
          created_at: string | null
          created_by: string
          description: string | null
          end_time: string | null
          event_type: string | null
          id: string
          jurisdiction_id: string | null
          location: string | null
          metadata: Json | null
          notification_sent: boolean | null
          reminder_time: unknown | null
          start_time: string
          status: string | null
          tenant_id: string
          title: string
          updated_at: string | null
        }
        Insert: {
          all_day?: boolean | null
          assigned_to?: string[] | null
          case_id?: string | null
          client_id?: string | null
          created_at?: string | null
          created_by: string
          description?: string | null
          end_time?: string | null
          event_type?: string | null
          id?: string
          jurisdiction_id?: string | null
          location?: string | null
          metadata?: Json | null
          notification_sent?: boolean | null
          reminder_time?: unknown | null
          start_time: string
          status?: string | null
          tenant_id: string
          title: string
          updated_at?: string | null
        }
        Update: {
          all_day?: boolean | null
          assigned_to?: string[] | null
          case_id?: string | null
          client_id?: string | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          end_time?: string | null
          event_type?: string | null
          id?: string
          jurisdiction_id?: string | null
          location?: string | null
          metadata?: Json | null
          notification_sent?: boolean | null
          reminder_time?: unknown | null
          start_time?: string
          status?: string | null
          tenant_id?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_case"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_client"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_jurisdiction"
            columns: ["jurisdiction_id"]
            isOneToOne: false
            referencedRelation: "jurisdictions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_tenant"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      firm_jurisdictions: {
        Row: {
          created_at: string | null
          firm_id: string
          id: string
          jurisdiction_id: string
          subscription_end_date: string | null
          subscription_start_date: string
          subscription_status: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          firm_id: string
          id?: string
          jurisdiction_id: string
          subscription_end_date?: string | null
          subscription_start_date?: string
          subscription_status?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          firm_id?: string
          id?: string
          jurisdiction_id?: string
          subscription_end_date?: string | null
          subscription_start_date?: string
          subscription_status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "firm_jurisdictions_firm_id_fkey"
            columns: ["firm_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "firm_jurisdictions_jurisdiction_id_fkey"
            columns: ["jurisdiction_id"]
            isOneToOne: false
            referencedRelation: "jurisdictions"
            referencedColumns: ["id"]
          },
        ]
      }
      firms: {
        Row: {
          address: Json
          admin_user_id: string | null
          created_at: string | null
          fax: string | null
          firm_type: string
          id: string
          jurisdiction_settings: Json | null
          mcp_secret_path: string | null
          mcp_status: string | null
          metadata: Json | null
          name: string
          phone: string
          practice_areas: string[] | null
          primary_email: string
          secondary_email: string | null
          settings: Json | null
          specializations: string[] | null
          state_bar_number: string
          status: string
          subscription_status: string
          subscription_tier: string
          tax_id: string | null
          tenant_id: string
          updated_at: string | null
          verification_status: string
          website_url: string | null
          year_established: number | null
        }
        Insert: {
          address?: Json
          admin_user_id?: string | null
          created_at?: string | null
          fax?: string | null
          firm_type: string
          id?: string
          jurisdiction_settings?: Json | null
          mcp_secret_path?: string | null
          mcp_status?: string | null
          metadata?: Json | null
          name: string
          phone: string
          practice_areas?: string[] | null
          primary_email: string
          secondary_email?: string | null
          settings?: Json | null
          specializations?: string[] | null
          state_bar_number: string
          status?: string
          subscription_status?: string
          subscription_tier?: string
          tax_id?: string | null
          tenant_id: string
          updated_at?: string | null
          verification_status?: string
          website_url?: string | null
          year_established?: number | null
        }
        Update: {
          address?: Json
          admin_user_id?: string | null
          created_at?: string | null
          fax?: string | null
          firm_type?: string
          id?: string
          jurisdiction_settings?: Json | null
          mcp_secret_path?: string | null
          mcp_status?: string | null
          metadata?: Json | null
          name?: string
          phone?: string
          practice_areas?: string[] | null
          primary_email?: string
          secondary_email?: string | null
          settings?: Json | null
          specializations?: string[] | null
          state_bar_number?: string
          status?: string
          subscription_status?: string
          subscription_tier?: string
          tax_id?: string | null
          tenant_id?: string
          updated_at?: string | null
          verification_status?: string
          website_url?: string | null
          year_established?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "firms_admin_user_id_fkey"
            columns: ["admin_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      holiday_calendars: {
        Row: {
          description: string | null
          holiday_date: string
          jurisdiction_id: string
        }
        Insert: {
          description?: string | null
          holiday_date: string
          jurisdiction_id: string
        }
        Update: {
          description?: string | null
          holiday_date?: string
          jurisdiction_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "holiday_calendars_jurisdiction_id_fkey"
            columns: ["jurisdiction_id"]
            isOneToOne: false
            referencedRelation: "jurisdictions"
            referencedColumns: ["id"]
          },
        ]
      }
      jurisdictions: {
        Row: {
          code: string
          country: string
          created_at: string | null
          id: string
          is_active: boolean | null
          name: string
          state_province: string | null
          updated_at: string | null
        }
        Insert: {
          code: string
          country: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          state_province?: string | null
          updated_at?: string | null
        }
        Update: {
          code?: string
          country?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          state_province?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      legal_rules: {
        Row: {
          case_type: string
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          is_business_days: boolean | null
          jurisdiction_id: string
          metadata: Json | null
          name: string
          rule_type: string
          updated_at: string | null
          value_days: number | null
        }
        Insert: {
          case_type: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_business_days?: boolean | null
          jurisdiction_id: string
          metadata?: Json | null
          name: string
          rule_type: string
          updated_at?: string | null
          value_days?: number | null
        }
        Update: {
          case_type?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_business_days?: boolean | null
          jurisdiction_id?: string
          metadata?: Json | null
          name?: string
          rule_type?: string
          updated_at?: string | null
          value_days?: number | null
        }
        Relationships: []
      }
      ml_models: {
        Row: {
          created_at: string | null
          id: string
          last_trained_at: string | null
          metrics: Json | null
          model_gcs_path: string | null
          model_name: string
          model_version: string
          tenant_id: string
          training_sample_size: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          last_trained_at?: string | null
          metrics?: Json | null
          model_gcs_path?: string | null
          model_name: string
          model_version: string
          tenant_id: string
          training_sample_size?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          last_trained_at?: string | null
          metrics?: Json | null
          model_gcs_path?: string | null
          model_name?: string
          model_version?: string
          tenant_id?: string
          training_sample_size?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ml_models_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      notification_schedules: {
        Row: {
          created_at: string | null
          data: Json | null
          id: string
          scheduled_for: string
          sent_at: string | null
          status: string
          tenant_id: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          id?: string
          scheduled_for: string
          sent_at?: string | null
          status?: string
          tenant_id: string
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          id?: string
          scheduled_for?: string
          sent_at?: string | null
          status?: string
          tenant_id?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notification_schedules_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      notifications: {
        Row: {
          context: Json | null
          created_at: string | null
          id: string
          message: string
          read: boolean | null
          sender_id: string | null
          severity: string | null
          tenant_id: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          context?: Json | null
          created_at?: string | null
          id?: string
          message: string
          read?: boolean | null
          sender_id?: string | null
          severity?: string | null
          tenant_id: string
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          context?: Json | null
          created_at?: string | null
          id?: string
          message?: string
          read?: boolean | null
          sender_id?: string | null
          severity?: string | null
          tenant_id?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      parties: {
        Row: {
          address: Json
          business_name: string | null
          created_at: string | null
          date_of_birth: string | null
          email: string | null
          first_name: string
          id: string
          last_name: string
          phone_primary: string | null
          phone_secondary: string | null
          preferred_contact_method: string | null
          ssn: string | null
          tenant_id: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          address?: Json
          business_name?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email?: string | null
          first_name: string
          id?: string
          last_name: string
          phone_primary?: string | null
          phone_secondary?: string | null
          preferred_contact_method?: string | null
          ssn?: string | null
          tenant_id?: string | null
          type: string
          updated_at?: string | null
        }
        Update: {
          address?: Json
          business_name?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email?: string | null
          first_name?: string
          id?: string
          last_name?: string
          phone_primary?: string | null
          phone_secondary?: string | null
          preferred_contact_method?: string | null
          ssn?: string | null
          tenant_id?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "parties_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      quota_adjustments: {
        Row: {
          adjustment_amount: number
          created_at: string
          created_by: string | null
          expires_at: string | null
          id: string
          reason: string | null
          tenant_id: string
          updated_at: string
          usage_type: string
        }
        Insert: {
          adjustment_amount: number
          created_at?: string
          created_by?: string | null
          expires_at?: string | null
          id?: string
          reason?: string | null
          tenant_id: string
          updated_at?: string
          usage_type: string
        }
        Update: {
          adjustment_amount?: number
          created_at?: string
          created_by?: string | null
          expires_at?: string | null
          id?: string
          reason?: string | null
          tenant_id?: string
          updated_at?: string
          usage_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "quota_adjustments_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      resource_usage: {
        Row: {
          created_at: string | null
          id: string
          period_end: string
          period_start: string
          resource_size_bytes: number | null
          tenant_id: string
          usage_count: number
          usage_type: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          period_end: string
          period_start: string
          resource_size_bytes?: number | null
          tenant_id: string
          usage_count?: number
          usage_type: string
        }
        Update: {
          created_at?: string | null
          id?: string
          period_end?: string
          period_start?: string
          resource_size_bytes?: number | null
          tenant_id?: string
          usage_count?: number
          usage_type?: string
        }
        Relationships: []
      }
      subscription_addons: {
        Row: {
          category: string
          code: string
          created_at: string | null
          description: string | null
          features: Json
          id: string
          is_active: boolean | null
          name: string
          price_monthly: number
          price_yearly: number
          updated_at: string | null
        }
        Insert: {
          category: string
          code: string
          created_at?: string | null
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean | null
          name: string
          price_monthly: number
          price_yearly: number
          updated_at?: string | null
        }
        Update: {
          category?: string
          code?: string
          created_at?: string | null
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean | null
          name?: string
          price_monthly?: number
          price_yearly?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      subscription_plans: {
        Row: {
          base_price_monthly: number
          base_price_yearly: number
          code: string
          created_at: string | null
          description: string | null
          features: Json
          id: string
          is_active: boolean | null
          is_public: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          base_price_monthly: number
          base_price_yearly: number
          code: string
          created_at?: string | null
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean | null
          is_public?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          base_price_monthly?: number
          base_price_yearly?: number
          code?: string
          created_at?: string | null
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean | null
          is_public?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      task_deadlines: {
        Row: {
          calc_steps: Json | null
          created_at: string
          deadline_date: string
          id: string
          overridden: boolean | null
          rule_citation: string | null
          task_id: string
          tenant_id: string
          updated_at: string
        }
        Insert: {
          calc_steps?: Json | null
          created_at?: string
          deadline_date: string
          id?: string
          overridden?: boolean | null
          rule_citation?: string | null
          task_id: string
          tenant_id: string
          updated_at?: string
        }
        Update: {
          calc_steps?: Json | null
          created_at?: string
          deadline_date?: string
          id?: string
          overridden?: boolean | null
          rule_citation?: string | null
          task_id?: string
          tenant_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "task_deadlines_fk_tenant"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
          {
            foreignKeyName: "task_deadlines_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      task_history: {
        Row: {
          change_type: string
          changed_at: string | null
          changed_by: string
          id: string
          metadata: Json | null
          new_values: Json | null
          previous_values: Json | null
          task_id: string
          tenant_id: string
        }
        Insert: {
          change_type: string
          changed_at?: string | null
          changed_by: string
          id?: string
          metadata?: Json | null
          new_values?: Json | null
          previous_values?: Json | null
          task_id: string
          tenant_id: string
        }
        Update: {
          change_type?: string
          changed_at?: string | null
          changed_by?: string
          id?: string
          metadata?: Json | null
          new_values?: Json | null
          previous_values?: Json | null
          task_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "task_history_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_history_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      tasks: {
        Row: {
          ai_metadata: Json | null
          assigned_to: string | null
          created_at: string | null
          created_by: string
          description: string | null
          due_date: string | null
          id: string
          related_case: string | null
          status: string
          tenant_id: string
          title: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          ai_metadata?: Json | null
          assigned_to?: string | null
          created_at?: string | null
          created_by: string
          description?: string | null
          due_date?: string | null
          id?: string
          related_case?: string | null
          status?: string
          tenant_id: string
          title: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          ai_metadata?: Json | null
          assigned_to?: string | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          due_date?: string | null
          id?: string
          related_case?: string | null
          status?: string
          tenant_id?: string
          title?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tasks_assigned_to_tenant_user_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "tasks_created_by_tenant_user_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "tasks_related_case_fkey"
            columns: ["related_case"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
          {
            foreignKeyName: "tasks_updated_by_tenant_user_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["auth_user_id"]
          },
        ]
      }
      tenant_addons: {
        Row: {
          addon_id: string
          canceled_at: string | null
          created_at: string | null
          current_period_end: string
          current_period_start: string
          id: string
          metadata: Json | null
          quantity: number
          status: string
          subscription_id: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          addon_id: string
          canceled_at?: string | null
          created_at?: string | null
          current_period_end: string
          current_period_start: string
          id?: string
          metadata?: Json | null
          quantity?: number
          status?: string
          subscription_id: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          addon_id?: string
          canceled_at?: string | null
          created_at?: string | null
          current_period_end?: string
          current_period_start?: string
          id?: string
          metadata?: Json | null
          quantity?: number
          status?: string
          subscription_id?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_addons_addon_id_fkey"
            columns: ["addon_id"]
            isOneToOne: false
            referencedRelation: "subscription_addons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_addons_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "tenant_subscriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_addons_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      tenant_quotas: {
        Row: {
          created_at: string | null
          id: string
          max_concurrent_processing: number
          max_daily_uploads: number
          max_document_size_mb: number
          max_monthly_uploads: number
          plan_tier: string
          quota_limit: number | null
          reset_frequency: string | null
          subscription_id: string | null
          tenant_id: string
          updated_at: string | null
          usage_type: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          max_concurrent_processing?: number
          max_daily_uploads?: number
          max_document_size_mb?: number
          max_monthly_uploads?: number
          plan_tier?: string
          quota_limit?: number | null
          reset_frequency?: string | null
          subscription_id?: string | null
          tenant_id: string
          updated_at?: string | null
          usage_type?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          max_concurrent_processing?: number
          max_daily_uploads?: number
          max_document_size_mb?: number
          max_monthly_uploads?: number
          plan_tier?: string
          quota_limit?: number | null
          reset_frequency?: string | null
          subscription_id?: string | null
          tenant_id?: string
          updated_at?: string | null
          usage_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_quotas_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "tenant_subscriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_quotas_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: true
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      tenant_subscriptions: {
        Row: {
          billing_cycle: string
          canceled_at: string | null
          created_at: string | null
          current_period_end: string
          current_period_start: string
          id: string
          metadata: Json | null
          payment_provider: string | null
          payment_provider_subscription_id: string | null
          plan_id: string
          status: string
          tenant_id: string
          trial_end: string | null
          trial_start: string | null
          updated_at: string | null
        }
        Insert: {
          billing_cycle?: string
          canceled_at?: string | null
          created_at?: string | null
          current_period_end: string
          current_period_start: string
          id?: string
          metadata?: Json | null
          payment_provider?: string | null
          payment_provider_subscription_id?: string | null
          plan_id: string
          status?: string
          tenant_id: string
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
        }
        Update: {
          billing_cycle?: string
          canceled_at?: string | null
          created_at?: string | null
          current_period_end?: string
          current_period_start?: string
          id?: string
          metadata?: Json | null
          payment_provider?: string | null
          payment_provider_subscription_id?: string | null
          plan_id?: string
          status?: string
          tenant_id?: string
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_subscriptions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_subscriptions_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string
          role: string
          user_id: string
        }
        Insert: {
          created_at?: string
          role: string
          user_id: string
        }
        Update: {
          created_at?: string
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          auth_user_id: string | null
          avatar_url: string | null
          created_at: string | null
          email: string
          firm_role: string | null
          first_name: string | null
          id: string
          last_login: string | null
          last_name: string | null
          role: string
          settings: Json | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          auth_user_id?: string | null
          avatar_url?: string | null
          created_at?: string | null
          email: string
          firm_role?: string | null
          first_name?: string | null
          id?: string
          last_login?: string | null
          last_name?: string | null
          role: string
          settings?: Json | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          auth_user_id?: string | null
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          firm_role?: string | null
          first_name?: string | null
          id?: string
          last_login?: string | null
          last_name?: string | null
          role?: string
          settings?: Json | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
      voice_agent_configs: {
        Row: {
          agent_type: string
          api_key: string | null
          config_data: Json
          created_at: string
          id: string
          is_enabled: boolean
          tenant_id: string
          updated_at: string
          webhook_url: string | null
        }
        Insert: {
          agent_type: string
          api_key?: string | null
          config_data?: Json
          created_at?: string
          id?: string
          is_enabled?: boolean
          tenant_id: string
          updated_at?: string
          webhook_url?: string | null
        }
        Update: {
          agent_type?: string
          api_key?: string | null
          config_data?: Json
          created_at?: string
          id?: string
          is_enabled?: boolean
          tenant_id?: string
          updated_at?: string
          webhook_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_agent_configs_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
    }
    Views: {
      tenant_entitlements: {
        Row: {
          addon_features: Json | null
          plan_code: string | null
          plan_features: Json | null
          tenant_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_subscriptions_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "firms"
            referencedColumns: ["tenant_id"]
          },
        ]
      }
    }
    Functions: {
      add_quota_adjustment: {
        Args: {
          p_tenant_id: string
          p_usage_type: string
          p_adjustment_amount: number
          p_reason: string
          p_expires_at?: string
          p_created_by?: string
        }
        Returns: string
      }
      count_documents_for_case: {
        Args: {
          p_case_id: string
          p_tenant_id: string
        }
        Returns: number
      }
      count_deadlines_for_case: {
        Args: {
          p_case_id: string
          p_tenant_id: string
        }
        Returns: number
      }
      count_cases_by_status: {
        Args: {
          p_tenant_id: string
        }
        Returns: { status: string; count: number }[]
      }
      count_cases_by_practice_area: {
        Args: {
          p_tenant_id: string
        }
        Returns: { practice_area: string; count: number }[]
      }
      count_clients_by_status: {
        Args: {
          p_tenant_id: string
        }
        Returns: { status: string; count: number }[]
      }
      get_client_summary_stats: {
        Args: {
          p_tenant_id: string
        }
        Returns: { total: number; active: number; inactive: number }
      }
      apply_document_diff: {
        Args: { content: string; diff: Json }
        Returns: string
      }
      check_feature_access: {
        Args: { p_tenant_id: string; p_feature_key: string }
        Returns: boolean
      }
      check_quota_limit: {
        Args: {
          p_tenant_id: string
          p_usage_type: string
          p_increment_by: number
        }
        Returns: Json
      }
      check_quota_with_adjustments: {
        Args: {
          p_tenant_id: string
          p_usage_type: string
          p_increment_by: number
        }
        Returns: Json
      }
      create_client_intake: {
        Args: {
          p_client_data: Json
          p_case_data: Json
          p_other_parties: Json
          p_test_tenant_id?: string
          p_test_user_id?: string
        }
        Returns: Json
      }
      generate_document_diff: {
        Args: { old_content: string; new_content: string }
        Returns: Json
      }
      generate_voice_agent_api_key: {
        Args: { p_tenant_id: string; p_agent_type: string }
        Returns: string
      }
      get_document_version: {
        Args: { p_version_id: string }
        Returns: {
          id: string
          document_id: string
          version_number: number
          content: string
          variables_used: Json
          changed_by: string
          changed_at: string
          change_type: string
        }[]
      }
      get_document_version_history: {
        Args: { p_document_id: string }
        Returns: {
          id: string
          version_number: number
          changed_by: string
          changed_at: string
          change_type: string
          has_diff: boolean
          parent_version_id: string
        }[]
      }
      get_effective_quota_limit: {
        Args: { p_tenant_id: string; p_usage_type: string }
        Returns: number
      }
      get_token_usage_summary: {
        Args: { p_tenant_id: string }
        Returns: Json
      }
      increment_resource_usage: {
        Args: {
          p_tenant_id: string
          p_usage_type: string
          p_increment_by: number
          p_resource_size_bytes?: number
        }
        Returns: Json
      }
      reconstruct_document_version: {
        Args: { p_document_id: string; p_version_number: number }
        Returns: string
      }
      record_voice_agent_usage: {
        Args: {
          p_api_key: string
          p_duration_seconds: number
          p_call_data?: Json
        }
        Returns: Json
      }
      track_token_usage: {
        Args: {
          p_tenant_id: string
          p_input_tokens: number
          p_output_tokens: number
          p_model_name?: string
          p_feature_context?: string
        }
        Returns: Json
      }
      validate_voice_agent_api_key: {
        Args: { p_api_key: string }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  audit: {
    Enums: {},
  },
  extensions: {
    Enums: {},
  },
  graphql: {
    Enums: {},
  },
  graphql_public: {
    Enums: {},
  },
  pgsodium_masks: {
    Enums: {},
  },
  public: {
    Enums: {
      case_status: [
        "pre_conflict_check",
        "conflict_check_failed",
        "accepted",
        "active",
        "closed",
        "rejected",
        "archived",
      ],
    },
  },
  realtime: {
    Enums: {
      action: ["INSERT", "UPDATE", "DELETE", "TRUNCATE", "ERROR"],
      equality_op: ["eq", "neq", "lt", "lte", "gt", "gte", "in"],
    },
  },
  security: {
    Enums: {},
  },
  storage: {
    Enums: {},
  },
  tenants: {
    Enums: {
      insight_category: ["risk", "task", "calendar", "efficiency", "coaching"],
      insight_status: ["new", "snoozed", "resolved"],
    },
  },
} as const
