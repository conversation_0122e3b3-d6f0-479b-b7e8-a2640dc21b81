/**
 * MCP Provisioner Service
 * 
 * Handles automatic provisioning of MCP (Model Context Protocol) API keys
 * for new tenants. Creates API keys in the 'texas-laws-personalinjury' project
 * and stores them securely in Google Secret Manager.
 */

import { Api<PERSON>eysClient } from '@google-cloud/apikeys';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

// Environment configuration
const MCP_PROJECT = process.env.MCP_PROJECT || 'texas-laws-personalinjury';
const MCP_KEY_PROVISIONER_SA = process.env.MCP_KEY_PROVISIONER_SA || '<EMAIL>';

// Service accounts that need access to tenant secrets
const AILEX_SERVICE_ACCOUNTS = [
  `serviceAccount:tenant-onboard-function@${MCP_PROJECT}.iam.gserviceaccount.com`,
  // Add other service accounts that need access
];

// Cache for secret paths to improve latency (10-minute TTL)
const secretPathCache = new Map<string, { path: string; timestamp: number }>();
const CACHE_TTL = 10 * 60 * 1000; // 10 minutes

/**
 * Provisions MCP access for a new tenant by creating an API key
 * and storing it securely in Google Secret Manager.
 * 
 * @param tenantId - The unique identifier for the tenant
 * @returns Promise<string> - The secret path in format 'projects/.../secrets/.../versions/latest'
 * @throws Error if provisioning fails
 */
export async function provisionMcpAccess(tenantId: string): Promise<string> {
  if (!tenantId) {
    throw new Error('Tenant ID is required for MCP provisioning');
  }

  try {
    // Initialize Google Cloud clients for the MCP project
    const { apiKeysClient, secretManagerClient } = await getCloudClients();

    // Step 1: Create API key in MCP project
    console.log(`Creating API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
    const apiKey = await createApiKey(tenantId, apiKeysClient);

    // Step 2: Store API key in Secret Manager
    console.log(`Storing API key in Secret Manager for tenant: ${tenantId}`);
    const secretName = await storeApiKeyInSecretManager(tenantId, apiKey, secretManagerClient);

    // Step 3: Grant access to AiLex service accounts
    console.log(`Granting access to service accounts for tenant: ${tenantId}`);
    await grantSecretAccess(secretName, secretManagerClient);

    // Return the secret path with latest version
    const secretPath = `${secretName}/versions/latest`;

    // Cache the secret path for future use (improves deadline request latency)
    cacheSecretPath(tenantId, secretPath);

    console.log(`Successfully provisioned MCP access for tenant: ${tenantId}`, {
      secretPath,
      tenantId,
    });

    return secretPath;

  } catch (error) {
    console.error(`Failed to provision MCP access for tenant: ${tenantId}`, error);
    throw new Error(`MCP provisioning failed for tenant ${tenantId}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Initialize Google Cloud clients for MCP project operations
 */
async function getCloudClients() {
  try {
    // Create clients for the MCP project
    // The application's service account should have the necessary permissions
    const apiKeysClient = new ApiKeysClient({
      projectId: MCP_PROJECT,
    });

    const secretManagerClient = new SecretManagerServiceClient({
      projectId: MCP_PROJECT,
    });

    return { apiKeysClient, secretManagerClient };

  } catch (error) {
    console.error('Failed to initialize Google Cloud clients', {
      error: error instanceof Error ? error.message : String(error),
      mcpProject: MCP_PROJECT,
    });
    throw error;
  }
}

/**
 * Create API key for the tenant using Google API Keys service
 */
async function createApiKey(tenantId: string, apiKeysClient: ApiKeysClient): Promise<string> {
  const keyName = `mcp-rules-api-${tenantId}`;
  const displayName = `MCP Rules Engine API Key - ${tenantId}`;

  const [operation] = await apiKeysClient.createKey({
    parent: `projects/${MCP_PROJECT}/locations/global`,
    keyId: keyName,
    key: {
      displayName,
      restrictions: {
        // Add API restrictions if needed
        // For now, we'll create keys without specific API restrictions
      },
    },
  });

  // Wait for the operation to complete
  const [key] = await operation.promise();

  if (!key.keyString) {
    throw new Error(`Failed to create API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
  }

  console.log(`Created API key for tenant: ${tenantId}`, {
    keyName: key.name,
    displayName: key.displayName,
  });

  return key.keyString;
}

/**
 * Store API key in Secret Manager
 */
async function storeApiKeyInSecretManager(
  tenantId: string,
  apiKey: string,
  secretManagerClient: SecretManagerServiceClient
): Promise<string> {
  const secretId = `mcp-key-${tenantId}`;
  const parent = `projects/${MCP_PROJECT}`;

  // Create the secret
  const [secret] = await secretManagerClient.createSecret({
    parent,
    secretId,
    secret: {
      replication: {
        automatic: {},
      },
      labels: {
        tenant: tenantId,
        service: 'mcp-rules-engine',
        environment: 'default',
        managed_by: 'in-app-provisioner',
      },
    },
  });

  // Add the secret version with the API key
  const [version] = await secretManagerClient.addSecretVersion({
    parent: secret.name!,
    payload: {
      data: Buffer.from(apiKey, 'utf8'),
    },
  });

  console.log(`Stored API key in Secret Manager for tenant: ${tenantId}`, {
    secretName: secret.name,
    versionName: version.name,
  });

  return secret.name!;
}

/**
 * Grant access to the secret for AiLex service accounts
 */
async function grantSecretAccess(
  secretName: string,
  secretManagerClient: SecretManagerServiceClient
): Promise<void> {
  for (const member of AILEX_SERVICE_ACCOUNTS) {
    await secretManagerClient.setIamPolicy({
      resource: secretName,
      policy: {
        bindings: [
          {
            role: 'roles/secretmanager.secretAccessor',
            members: [member],
          },
        ],
      },
    });

    console.log(`Granted secret access to: ${member}`, {
      secretName,
      member,
    });
  }
}

/**
 * Get cached secret path for a tenant (for performance optimization)
 * This helps reduce latency for deadline requests that need secret access
 */
export function getCachedSecretPath(tenantId: string): string | null {
  const cached = secretPathCache.get(tenantId);

  if (!cached) {
    return null;
  }

  // Check if cache entry is still valid
  if (Date.now() - cached.timestamp > CACHE_TTL) {
    secretPathCache.delete(tenantId);
    return null;
  }

  return cached.path;
}

/**
 * Cache a secret path for a tenant
 */
export function cacheSecretPath(tenantId: string, secretPath: string): void {
  secretPathCache.set(tenantId, {
    path: secretPath,
    timestamp: Date.now(),
  });
}
