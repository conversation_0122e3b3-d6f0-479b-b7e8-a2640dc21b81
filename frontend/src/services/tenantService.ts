/**
 * Tenant Service
 * 
 * Handles tenant creation with automatic MCP (Model Context Protocol) provisioning.
 * Integrates with the MCP Provisioner to ensure 100% automatic tenant onboarding.
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../lib/supabase/database.types';
import { provisionMcpAccess } from './mcpProvisioner';

// Type definitions for tenant creation
export interface TenantInput {
  name: string;
  state_bar_number: string;
  firm_type: 'Solo Practice' | 'Partnership' | 'Professional Corporation' | 'Limited Liability Company';
  year_established?: number;
  tax_id?: string;
  primary_email: string;
  secondary_email?: string;
  phone: string;
  fax?: string;
  website_url?: string;
  address: {
    street?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    country?: string;
  };
  practice_areas?: string[];
  specializations?: string[];
  admin_user_id?: string;
  subscription_tier?: string;
  subscription_status?: string;
  settings?: Record<string, any>;
  metadata?: Record<string, any>;
  jurisdiction_settings?: Record<string, any>;
}

export interface TenantResult {
  id: string;
  tenant_id: string;
  name: string;
  mcp_status: 'pending_key' | 'active' | 'inactive' | 'key_provisioning_failed';
  mcp_secret_path?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Tenant Service Class
 * Provides methods for tenant management with automatic MCP provisioning
 */
export class TenantService {
  private supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Create a new tenant with automatic MCP provisioning
   * 
   * @param input - Tenant creation data
   * @returns Promise<TenantResult> - The created tenant with MCP provisioning status
   */
  async createTenant(input: TenantInput): Promise<TenantResult> {
    console.log('Starting tenant creation process', { name: input.name });

    try {
      // Step 1: Insert tenant row with status='pending_key'
      const tenantData = {
        ...input,
        tenant_id: crypto.randomUUID(), // Generate unique tenant ID
        address: JSON.stringify(input.address),
        practice_areas: input.practice_areas || [],
        specializations: input.specializations || [],
        settings: input.settings || {},
        metadata: input.metadata || {},
        jurisdiction_settings: input.jurisdiction_settings || {},
        status: 'active',
        verification_status: 'pending',
        subscription_tier: input.subscription_tier || 'basic',
        subscription_status: input.subscription_status || 'active',
        // mcp_status: 'pending_key' as const, // Temporarily disabled for testing
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log('Inserting tenant record with pending_key status', {
        tenant_id: tenantData.tenant_id,
        name: tenantData.name,
        subscription_status: tenantData.subscription_status,
        subscription_tier: tenantData.subscription_tier,
      });

      console.log('Full tenant data:', JSON.stringify(tenantData, null, 2));

      const { data: tenant, error: insertError } = await this.supabase
        .schema('tenants')
        .from('firms')
        .insert(tenantData)
        .select()
        .single();

      if (insertError) {
        console.error('Failed to insert tenant record', insertError);
        throw new Error(`Failed to create tenant: ${insertError.message}`);
      }

      console.log('Tenant record created successfully', {
        id: tenant.id,
        tenant_id: tenant.tenant_id,
      });

      // Step 2: Provision MCP access
      try {
        console.log('Starting MCP provisioning', { tenant_id: tenant.tenant_id });
        const secretPath = await provisionMcpAccess(tenant.tenant_id);

        // Step 3: Update tenant record with secret path and active status
        console.log('Updating tenant record with MCP provisioning results', {
          tenant_id: tenant.tenant_id,
          secretPath,
        });

        const { data: updatedTenant, error: updateError } = await this.supabase
          .schema('tenants')
          .from('firms')
          .update({
            mcp_secret_path: secretPath,
            mcp_status: 'active' as const,
            updated_at: new Date().toISOString(),
          })
          .eq('tenant_id', tenant.tenant_id)
          .select()
          .single();

        if (updateError) {
          console.error('Failed to update tenant with MCP provisioning results', updateError);
          throw new Error(`Failed to update tenant with MCP results: ${updateError.message}`);
        }

        console.log('Tenant creation completed successfully', {
          tenant_id: updatedTenant.tenant_id,
          mcp_status: updatedTenant.mcp_status,
          mcp_secret_path: updatedTenant.mcp_secret_path,
        });

        return {
          id: updatedTenant.id,
          tenant_id: updatedTenant.tenant_id,
          name: updatedTenant.name,
          mcp_status: updatedTenant.mcp_status as 'pending_key' | 'active' | 'inactive' | 'key_provisioning_failed',
          mcp_secret_path: updatedTenant.mcp_secret_path,
          created_at: updatedTenant.created_at,
          updated_at: updatedTenant.updated_at,
        };

      } catch (mcpError) {
        console.error('MCP provisioning failed', {
          tenant_id: tenant.tenant_id,
          error: mcpError,
        });

        // Update tenant status to failed
        await this.supabase
          .schema('tenants')
          .from('firms')
          .update({
            mcp_status: 'key_provisioning_failed' as const,
            updated_at: new Date().toISOString(),
          })
          .eq('tenant_id', tenant.tenant_id);

        throw new Error(`MCP provisioning failed: ${mcpError instanceof Error ? mcpError.message : String(mcpError)}`);
      }

    } catch (error) {
      console.error('Tenant creation failed', {
        name: input.name,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Get tenant by ID
   */
  async getTenantById(tenantId: string): Promise<TenantResult | null> {
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('firms')
      .select('id, tenant_id, name, mcp_status, mcp_secret_path, created_at, updated_at')
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get tenant: ${error.message}`);
    }

    return {
      ...data,
      mcp_status: data.mcp_status as 'pending_key' | 'active' | 'inactive' | 'key_provisioning_failed'
    };
  }

  /**
   * Retry MCP provisioning for a failed tenant
   */
  async retryMcpProvisioning(tenantId: string): Promise<TenantResult> {
    console.log('Retrying MCP provisioning', { tenant_id: tenantId });

    const tenant = await this.getTenantById(tenantId);
    if (!tenant) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    if (tenant.mcp_status === 'active') {
      console.log('Tenant already has active MCP status', { tenant_id: tenantId });
      return tenant;
    }

    // Set status to pending_key
    await this.supabase
      .schema('tenants')
      .from('firms')
      .update({
        mcp_status: 'pending_key' as const,
        updated_at: new Date().toISOString(),
      })
      .eq('tenant_id', tenantId);

    try {
      // Provision MCP access
      const secretPath = await provisionMcpAccess(tenantId);

      // Update with success
      const { data: updatedTenant, error } = await this.supabase
        .schema('tenants')
        .from('firms')
        .update({
          mcp_secret_path: secretPath,
          mcp_status: 'active' as const,
          updated_at: new Date().toISOString(),
        })
        .eq('tenant_id', tenantId)
        .select('id, tenant_id, name, mcp_status, mcp_secret_path, created_at, updated_at')
        .single();

      if (error) {
        throw new Error(`Failed to update tenant: ${error.message}`);
      }

      return {
        ...updatedTenant,
        mcp_status: updatedTenant.mcp_status as 'pending_key' | 'active' | 'inactive' | 'key_provisioning_failed'
      };

    } catch (error) {
      // Update with failure
      await this.supabase
        .schema('tenants')
        .from('firms')
        .update({
          mcp_status: 'key_provisioning_failed' as const,
          updated_at: new Date().toISOString(),
        })
        .eq('tenant_id', tenantId);

      throw error;
    }
  }
}

/**
 * Factory function to create a TenantService instance
 */
export function createTenantService(supabase: SupabaseClient<Database>): TenantService {
  return new TenantService(supabase);
}
