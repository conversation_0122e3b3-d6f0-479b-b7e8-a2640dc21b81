#!/usr/bin/env ts-node
/**
 * Test script to find valid subscription statuses
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function testSubscriptionStatuses() {
  console.log('🧪 Testing Subscription Status Values');
  console.log('='.repeat(50));

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  const testValues = ['active', 'inactive', 'trial', 'trialing', 'pending', 'cancelled', 'suspended'];
  
  for (const status of testValues) {
    const testData = {
      tenant_id: '00000000-0000-0000-0000-000000000001',
      name: 'Test Firm',
      state_bar_number: 'TX123',
      firm_type: 'Partnership',
      primary_email: '<EMAIL>',
      phone: '******-123-4567',
      admin_user_id: 'f4b97960-9b95-431f-b2db-5fde30f12b70', // Use existing admin user ID
      subscription_status: status,
      address: '{}',
      practice_areas: [],
      specializations: [],
      settings: {},
      metadata: {},
      jurisdiction_settings: {}
    };
    
    const { error: insertError } = await supabase
      .schema('tenants')
      .from('firms')
      .insert(testData);
      
    if (!insertError) {
      console.log(`✅ '${status}' is valid`);
      // Clean up
      await supabase.schema('tenants').from('firms').delete().eq('tenant_id', testData.tenant_id);
    } else {
      console.log(`❌ '${status}' failed: ${insertError.message}`);
    }
  }
}

testSubscriptionStatuses().catch(console.error);
