#!/usr/bin/env ts-node
/**
 * Test insertion without any MCP-related fields
 * to see if that's causing the constraint issue
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function testWithoutMcp() {
  console.log('🔍 Testing Insertion Without MCP Fields');
  console.log('='.repeat(45));

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  try {
    // Get existing record for reference
    const { data: existingRecords } = await supabase
      .schema('tenants')
      .from('firms')
      .select('*')
      .limit(1);

    const existing = existingRecords?.[0];
    if (!existing) {
      console.log('❌ No existing record found');
      return;
    }

    console.log('📋 Reference record:');
    console.log(`   subscription_status: ${existing.subscription_status}`);
    console.log(`   subscription_tier: ${existing.subscription_tier}`);
    console.log(`   mcp_status: ${existing.mcp_status}`);

    // Test 1: Minimal data without MCP fields
    console.log('\n🧪 Test 1: Minimal data (no MCP fields)...');
    
    const minimalData = {
      tenant_id: '00000000-0000-0000-0000-000000000006',
      name: 'Minimal Test Firm',
      state_bar_number: 'MIN789',
      firm_type: 'Solo Practice',
      primary_email: '<EMAIL>',
      phone: '************',
      admin_user_id: existing.admin_user_id,
      subscription_status: 'active',
      subscription_tier: 'basic',
      status: 'active',
      verification_status: 'pending',
    };

    const { error: minimalError } = await supabase
      .schema('tenants')
      .from('firms')
      .insert(minimalData);

    if (minimalError) {
      console.log('❌ Minimal insert failed:', minimalError.message);
      console.log('   Error details:', minimalError);
    } else {
      console.log('✅ Minimal insert succeeded!');
      await supabase.schema('tenants').from('firms').delete().eq('tenant_id', minimalData.tenant_id);
    }

    // Test 2: Try with explicit NULL for MCP fields
    console.log('\n🧪 Test 2: With explicit NULL for MCP fields...');
    
    const nullMcpData = {
      ...minimalData,
      tenant_id: '00000000-0000-0000-0000-000000000007',
      name: 'Null MCP Test Firm',
      mcp_status: null,
      mcp_secret_path: null,
    };

    const { error: nullMcpError } = await supabase
      .schema('tenants')
      .from('firms')
      .insert(nullMcpData);

    if (nullMcpError) {
      console.log('❌ Null MCP insert failed:', nullMcpError.message);
    } else {
      console.log('✅ Null MCP insert succeeded!');
      await supabase.schema('tenants').from('firms').delete().eq('tenant_id', nullMcpData.tenant_id);
    }

    // Test 3: Try different subscription statuses one by one
    console.log('\n🧪 Test 3: Testing different subscription statuses...');
    
    const statusesToTest = ['active', 'inactive', 'trial', 'pending', 'cancelled'];
    
    for (const status of statusesToTest) {
      const statusTestData = {
        tenant_id: `00000000-0000-0000-0000-00000000000${statusesToTest.indexOf(status) + 8}`,
        name: `Status Test ${status}`,
        state_bar_number: `STS${statusesToTest.indexOf(status)}`,
        firm_type: 'Solo Practice',
        primary_email: `${status}@test.com`,
        phone: '************',
        admin_user_id: existing.admin_user_id,
        subscription_status: status,
        subscription_tier: 'basic',
        status: 'active',
        verification_status: 'pending',
      };

      const { error: statusError } = await supabase
        .schema('tenants')
        .from('firms')
        .insert(statusTestData);

      if (statusError) {
        console.log(`❌ Status '${status}' failed: ${statusError.message}`);
      } else {
        console.log(`✅ Status '${status}' succeeded!`);
        await supabase.schema('tenants').from('firms').delete().eq('tenant_id', statusTestData.tenant_id);
      }
    }

    // Test 4: Check if there's a specific pattern in the constraint
    console.log('\n🧪 Test 4: Testing constraint hypothesis...');
    
    // Maybe the constraint checks that subscription_status can only be 'active' 
    // when certain other conditions are met?
    
    const hypothesisData = {
      tenant_id: '00000000-0000-0000-0000-000000000020',
      name: 'Hypothesis Test',
      state_bar_number: 'HYP123',
      firm_type: 'Solo Practice',
      primary_email: '<EMAIL>',
      phone: '************',
      admin_user_id: existing.admin_user_id,
      subscription_status: 'active',
      subscription_tier: 'basic',
      status: 'active',
      verification_status: 'verified', // Try different verification status
    };

    const { error: hypothesisError } = await supabase
      .schema('tenants')
      .from('firms')
      .insert(hypothesisData);

    if (hypothesisError) {
      console.log('❌ Hypothesis test failed:', hypothesisError.message);
    } else {
      console.log('✅ Hypothesis test succeeded! verification_status might matter');
      await supabase.schema('tenants').from('firms').delete().eq('tenant_id', hypothesisData.tenant_id);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testWithoutMcp().catch(console.error);
