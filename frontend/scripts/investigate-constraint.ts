#!/usr/bin/env ts-node
/**
 * Investigate the firms_subscription_status_check constraint
 * to understand what values are actually allowed
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function investigateConstraint() {
  console.log('🔍 Investigating Database Constraint: firms_subscription_status_check');
  console.log('='.repeat(70));

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  try {
    // First, let's get the constraint definition
    console.log('📋 Step 1: Getting constraint definition...');
    
    const { data: constraintData, error: constraintError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            conname as constraint_name,
            pg_get_constraintdef(oid) as constraint_definition
          FROM pg_constraint 
          WHERE conrelid = (
            SELECT oid 
            FROM pg_class 
            WHERE relname = 'firms' 
            AND relnamespace = (
              SELECT oid 
              FROM pg_namespace 
              WHERE nspname = 'tenants'
            )
          )
          AND contype = 'c'
          ORDER BY conname;
        `
      });

    if (constraintError) {
      console.log('❌ Could not query constraints directly. Error:', constraintError.message);
      
      // Alternative approach: Check information_schema
      console.log('📋 Trying alternative approach via information_schema...');
      
      const { data: schemaData, error: schemaError } = await supabase
        .rpc('sql', {
          query: `
            SELECT 
              constraint_name,
              check_clause
            FROM information_schema.check_constraints 
            WHERE constraint_schema = 'tenants'
            AND constraint_name LIKE '%subscription_status%';
          `
        });
        
      if (schemaError) {
        console.log('❌ Schema query failed:', schemaError.message);
      } else {
        console.log('✅ Found constraints via information_schema:');
        console.log(JSON.stringify(schemaData, null, 2));
      }
    } else {
      console.log('✅ Found constraints:');
      console.log(JSON.stringify(constraintData, null, 2));
    }

    // Step 2: Analyze existing data to understand valid values
    console.log('\n📊 Step 2: Analyzing existing subscription_status values...');
    
    const { data: statusData, error: statusError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('subscription_status, subscription_tier, status, verification_status')
      .limit(10);

    if (statusError) {
      console.log('❌ Could not fetch existing data:', statusError.message);
    } else {
      console.log('✅ Sample of existing records:');
      statusData.forEach((record, index) => {
        console.log(`  ${index + 1}. subscription_status: "${record.subscription_status}", subscription_tier: "${record.subscription_tier}"`);
        console.log(`     status: "${record.status}", verification_status: "${record.verification_status}"`);
      });
      
      // Get unique values
      const uniqueStatuses = [...new Set(statusData.map(r => r.subscription_status))];
      const uniqueTiers = [...new Set(statusData.map(r => r.subscription_tier))];
      
      console.log('\n📈 Unique subscription_status values in database:', uniqueStatuses);
      console.log('📈 Unique subscription_tier values in database:', uniqueTiers);
    }

    // Step 3: Test the constraint by trying different combinations
    console.log('\n🧪 Step 3: Testing constraint with different value combinations...');
    
    const testCombinations = [
      { subscription_status: 'active', subscription_tier: 'basic' },
      { subscription_status: 'active', subscription_tier: 'premium' },
      { subscription_status: 'inactive', subscription_tier: 'basic' },
      { subscription_status: 'trial', subscription_tier: 'basic' },
      { subscription_status: 'trialing', subscription_tier: 'basic' },
      { subscription_status: 'pending', subscription_tier: 'basic' },
      { subscription_status: 'cancelled', subscription_tier: 'basic' },
    ];

    for (const combo of testCombinations) {
      const testData = {
        tenant_id: '00000000-0000-0000-0000-000000000003',
        name: 'Constraint Test Firm',
        state_bar_number: 'TEST123',
        firm_type: 'Partnership',
        primary_email: '<EMAIL>',
        phone: '******-123-4567',
        admin_user_id: 'f4b97960-9b95-431f-b2db-5fde30f12b70',
        address: '{}',
        practice_areas: [],
        specializations: [],
        settings: {},
        metadata: {},
        jurisdiction_settings: {},
        ...combo
      };
      
      const { error: insertError } = await supabase
        .schema('tenants')
        .from('firms')
        .insert(testData);
        
      if (!insertError) {
        console.log(`✅ SUCCESS: subscription_status="${combo.subscription_status}", subscription_tier="${combo.subscription_tier}"`);
        // Clean up
        await supabase.schema('tenants').from('firms').delete().eq('tenant_id', testData.tenant_id);
      } else {
        console.log(`❌ FAILED: subscription_status="${combo.subscription_status}", subscription_tier="${combo.subscription_tier}"`);
        console.log(`   Error: ${insertError.message}`);
        
        // If it's not the subscription status constraint, show the full error
        if (!insertError.message.includes('firms_subscription_status_check')) {
          console.log(`   Full error:`, insertError);
        }
      }
    }

    // Step 4: Check if there are any enum types defined
    console.log('\n🔤 Step 4: Checking for custom enum types...');
    
    const { data: enumData, error: enumError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            t.typname as enum_name,
            e.enumlabel as enum_value
          FROM pg_type t 
          JOIN pg_enum e ON t.oid = e.enumtypid  
          WHERE t.typname LIKE '%subscription%'
          ORDER BY t.typname, e.enumsortorder;
        `
      });

    if (enumError) {
      console.log('❌ Could not query enum types:', enumError.message);
    } else if (enumData && enumData.length > 0) {
      console.log('✅ Found subscription-related enum types:');
      console.log(JSON.stringify(enumData, null, 2));
    } else {
      console.log('ℹ️  No subscription-related enum types found');
    }

  } catch (error) {
    console.error('❌ Investigation failed:', error);
  }
}

investigateConstraint().catch(console.error);
