#!/usr/bin/env ts-node
/**
 * Test script for tenant creation with MCP provisioning
 * 
 * This script tests the end-to-end tenant creation flow including
 * automatic MCP API key provisioning.
 */

import { createClient } from '@supabase/supabase-js';
import { createTenantService } from '../src/services/tenantService';

// Load environment variables
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function testTenantCreation() {
  console.log('🧪 Testing Tenant Creation with MCP Provisioning');
  console.log('='.repeat(60));

  try {
    // Create Supabase client
    console.log('📡 Creating Supabase client...');
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

    // Create tenant service
    console.log('🏗️  Creating tenant service...');
    const tenantService = createTenantService(supabase);

    // Test tenant data - minimal required fields first
    const timestamp = Date.now();
    const testTenant = {
      name: `Production Test LLP ${timestamp}`,
      state_bar_number: `TX${timestamp}`,
      firm_type: 'Partnership' as const,
      primary_email: `admin-${timestamp}@test-llp.com`,
      phone: '******-TEST-123',
      address: {
        street: '123 Test Street',
        city: 'Austin',
        state: 'TX',
        zip_code: '78701'
      }
    };

    console.log('🚀 Creating tenant:', testTenant.name);
    console.log('📧 Email:', testTenant.primary_email);
    console.log('📍 Location:', `${testTenant.address.city}, ${testTenant.address.state}`);

    // Create tenant
    const startTime = Date.now();
    const result = await tenantService.createTenant(testTenant);
    const endTime = Date.now();

    console.log('\n✅ Tenant Creation Successful!');
    console.log('='.repeat(60));
    console.log('📊 Results:');
    console.log(`   • Tenant ID: ${result.tenant_id}`);
    console.log(`   • Name: ${result.name}`);
    console.log(`   • MCP Status: ${result.mcp_status}`);
    console.log(`   • Secret Path: ${result.mcp_secret_path || 'Not set'}`);
    console.log(`   • Created At: ${result.created_at}`);
    console.log(`   • Processing Time: ${endTime - startTime}ms`);

    // Verify the tenant was created correctly
    console.log('\n🔍 Verifying tenant creation...');
    const retrievedTenant = await tenantService.getTenantById(result.tenant_id);
    
    if (retrievedTenant) {
      console.log('✅ Tenant verification successful');
      console.log(`   • Retrieved Name: ${retrievedTenant.name}`);
      console.log(`   • Retrieved MCP Status: ${retrievedTenant.mcp_status}`);
    } else {
      console.log('❌ Tenant verification failed - could not retrieve tenant');
    }

    // Test MCP provisioning status
    if (result.mcp_status === 'active' && result.mcp_secret_path) {
      console.log('\n🔐 MCP Provisioning Status: SUCCESS');
      console.log(`   • Secret Path: ${result.mcp_secret_path}`);
      console.log('   • API key should be available in Secret Manager');
    } else if (result.mcp_status === 'pending_key') {
      console.log('\n⏳ MCP Provisioning Status: PENDING');
      console.log('   • Provisioning may still be in progress');
    } else if (result.mcp_status === 'key_provisioning_failed') {
      console.log('\n❌ MCP Provisioning Status: FAILED');
      console.log('   • Check logs for provisioning errors');
      
      // Test retry functionality
      console.log('\n🔄 Testing MCP provisioning retry...');
      try {
        const retryResult = await tenantService.retryMcpProvisioning(result.tenant_id);
        console.log('✅ Retry completed');
        console.log(`   • New MCP Status: ${retryResult.mcp_status}`);
        console.log(`   • Secret Path: ${retryResult.mcp_secret_path || 'Not set'}`);
      } catch (retryError) {
        console.log('❌ Retry failed:', retryError);
      }
    }

    console.log('\n🎉 Test completed successfully!');
    console.log('='.repeat(60));

    return result;

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error('='.repeat(60));
    
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Stack trace:', error.stack);
    }
    
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testTenantCreation()
    .then((result) => {
      console.log('\n✅ All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Tests failed!');
      process.exit(1);
    });
}

export { testTenantCreation };
