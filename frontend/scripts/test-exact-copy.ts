#!/usr/bin/env ts-node
/**
 * Test script to copy an existing record exactly
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function testExactCopy() {
  console.log('🧪 Testing Exact Copy of Existing Record');
  console.log('='.repeat(50));

  console.log('Environment check:');
  console.log('SUPABASE_URL:', SUPABASE_URL ? 'Set' : 'Not set');
  console.log('SUPABASE_SERVICE_KEY:', SUPABASE_SERVICE_KEY ? 'Set' : 'Not set');

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  // Get an existing record
  const { data: existingRecords, error: fetchError } = await supabase
    .schema('tenants')
    .from('firms')
    .select('*')
    .limit(1);
    
  if (fetchError || !existingRecords || existingRecords.length === 0) {
    console.log('❌ Could not fetch existing record:', fetchError);
    return;
  }
  
  const existing = existingRecords[0];
  console.log('📋 Found existing record:', existing.name);
  
  // Create a copy with a new tenant_id
  const copyData = {
    ...existing,
    id: undefined, // Let database generate new ID
    tenant_id: '00000000-0000-0000-0000-000000000002',
    name: 'Copy of ' + existing.name,
    state_bar_number: 'COPY123',
    primary_email: '<EMAIL>',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  console.log('🔄 Attempting to insert copy...');
  console.log('Subscription status:', copyData.subscription_status);
  
  const { data: insertedData, error: insertError } = await supabase
    .schema('tenants')
    .from('firms')
    .insert(copyData)
    .select();
    
  if (insertError) {
    console.log('❌ Insert failed:', insertError.message);
    console.log('Full error:', insertError);
  } else {
    console.log('✅ Insert successful!');
    console.log('Inserted record:', insertedData[0]?.name);
    
    // Clean up
    await supabase.schema('tenants').from('firms').delete().eq('tenant_id', copyData.tenant_id);
    console.log('🧹 Cleaned up test record');
  }
}

testExactCopy().catch(console.error);
