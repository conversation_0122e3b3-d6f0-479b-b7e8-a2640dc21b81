#!/usr/bin/env ts-node
/**
 * Test the hypothesis that new tenants should start with subscription_status='trialing'
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function testTrialingStatus() {
  console.log('🔍 Testing Hypothesis: New Tenants Should Start with subscription_status="trialing"');
  console.log('='.repeat(80));

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  try {
    // Get existing record for reference
    const { data: existingRecords } = await supabase
      .schema('tenants')
      .from('firms')
      .select('admin_user_id')
      .limit(1);

    const existing = existingRecords?.[0];
    if (!existing) {
      console.log('❌ No existing record found');
      return;
    }

    // Test with subscription_status='trialing'
    console.log('🧪 Testing with subscription_status="trialing"...');
    
    const trialingData = {
      tenant_id: '00000000-0000-0000-0000-000000000030',
      name: 'Trialing Test Firm',
      state_bar_number: 'TRI123',
      firm_type: 'Solo Practice',
      primary_email: '<EMAIL>',
      phone: '************',
      admin_user_id: existing.admin_user_id,
      subscription_status: 'trialing',
      subscription_tier: 'basic',
      status: 'active',
      verification_status: 'pending',
    };

    const { data: trialingResult, error: trialingError } = await supabase
      .schema('tenants')
      .from('firms')
      .insert(trialingData)
      .select();

    if (trialingError) {
      console.log('❌ Trialing status failed:', trialingError.message);
      console.log('   Full error:', trialingError);
    } else {
      console.log('✅ SUCCESS! subscription_status="trialing" works!');
      console.log('   Created tenant:', trialingResult[0]?.name);
      console.log('   Subscription status:', trialingResult[0]?.subscription_status);
      
      // Now test if we can update it to 'active'
      console.log('\n🔄 Testing update from "trialing" to "active"...');
      
      const { data: updateResult, error: updateError } = await supabase
        .schema('tenants')
        .from('firms')
        .update({ subscription_status: 'active' })
        .eq('tenant_id', trialingData.tenant_id)
        .select();

      if (updateError) {
        console.log('❌ Update to active failed:', updateError.message);
      } else {
        console.log('✅ Update to active succeeded!');
        console.log('   Updated subscription status:', updateResult[0]?.subscription_status);
      }
      
      // Clean up
      await supabase.schema('tenants').from('firms').delete().eq('tenant_id', trialingData.tenant_id);
      console.log('🧹 Cleaned up test record');
    }

    // Test other potential valid statuses for new tenants
    console.log('\n🧪 Testing other potential valid statuses for new tenants...');
    
    const statusesToTest = ['trial', 'pending', 'inactive'];
    
    for (const status of statusesToTest) {
      const testData = {
        tenant_id: `00000000-0000-0000-0000-00000000003${statusesToTest.indexOf(status) + 1}`,
        name: `${status} Test Firm`,
        state_bar_number: `${status.toUpperCase()}123`,
        firm_type: 'Solo Practice',
        primary_email: `${status}@test.com`,
        phone: '************',
        admin_user_id: existing.admin_user_id,
        subscription_status: status,
        subscription_tier: 'basic',
        status: 'active',
        verification_status: 'pending',
      };

      const { error: statusError } = await supabase
        .schema('tenants')
        .from('firms')
        .insert(testData);

      if (statusError) {
        console.log(`❌ Status '${status}' failed: ${statusError.message}`);
      } else {
        console.log(`✅ Status '${status}' succeeded!`);
        await supabase.schema('tenants').from('firms').delete().eq('tenant_id', testData.tenant_id);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testTrialingStatus().catch(console.error);
