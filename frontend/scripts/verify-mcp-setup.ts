#!/usr/bin/env ts-node
/**
 * MCP Setup Verification Script
 * 
 * Verifies that all required components are properly configured
 * for the new in-app MCP provisioning system.
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = join(process.cwd(), '.env');
  if (existsSync(envPath)) {
    const envContent = readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value && !process.env[key]) {
        process.env[key] = value.replace(/^["']|["']$/g, '');
      }
    });
  }
}

// Load .env file
loadEnvFile();

// Environment variables from .env
const MCP_PROJECT = process.env.MCP_PROJECT || 'texas-laws-personalinjury';
const TENANT_PROJECT = process.env.TENANT_PROJECT || 'new-texas-laws';
const GOOGLE_CLOUD_PROJECT = process.env.GOOGLE_CLOUD_PROJECT || 'new-texas-laws';

interface CheckResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  command?: string;
}

const results: CheckResult[] = [];

function runCheck(name: string, checkFn: () => CheckResult): void {
  try {
    const result = checkFn();
    results.push(result);
  } catch (error) {
    results.push({
      name,
      status: 'FAIL',
      message: `Error running check: ${error instanceof Error ? error.message : String(error)}`
    });
  }
}

function execCommand(command: string): string {
  try {
    return execSync(command, { encoding: 'utf8', stdio: 'pipe' });
  } catch (error) {
    throw new Error(`Command failed: ${command}`);
  }
}

// Check 1: Backend Service Account IAM
runCheck('Backend SA IAM', () => {
  try {
    const policy = execCommand(`gcloud projects get-iam-policy ${MCP_PROJECT} --format=json`);
    const policyObj = JSON.parse(policy);
    
    const requiredRoles = ['roles/serviceusage.apiKeysAdmin', 'roles/secretmanager.admin'];
    const bindings = policyObj.bindings || [];
    
    const hasRequiredRoles = requiredRoles.every(role => 
      bindings.some((binding: any) => binding.role === role)
    );

    if (hasRequiredRoles) {
      return {
        name: 'Backend SA IAM',
        status: 'PASS',
        message: `Service account has required roles in ${MCP_PROJECT}`,
        command: `gcloud projects get-iam-policy ${MCP_PROJECT} | grep -E "(apikeys.admin|secretmanager.admin)"`
      };
    } else {
      return {
        name: 'Backend SA IAM',
        status: 'FAIL',
        message: `Missing required roles (serviceusage.apiKeysAdmin, secretmanager.admin) in ${MCP_PROJECT}`,
        command: `gcloud projects add-iam-policy-binding ${MCP_PROJECT} --member="serviceAccount:YOUR-SA@${TENANT_PROJECT}.iam.gserviceaccount.com" --role="roles/serviceusage.apiKeysAdmin"`
      };
    }
  } catch (error) {
    return {
      name: 'Backend SA IAM',
      status: 'WARN',
      message: 'Could not verify IAM policy. Please check manually.',
      command: `gcloud projects get-iam-policy ${MCP_PROJECT} | grep <your-SA>`
    };
  }
});

// Check 2: API Keys API Enabled
runCheck('API Keys API', () => {
  try {
    const services = execCommand(`gcloud services list --enabled --project=${MCP_PROJECT} --format="value(name)"`);
    const isEnabled = services.includes('apikeys.googleapis.com');
    
    return {
      name: 'API Keys API',
      status: isEnabled ? 'PASS' : 'FAIL',
      message: isEnabled ? 'API Keys API is enabled' : 'API Keys API is not enabled',
      command: isEnabled ? '' : `gcloud services enable apikeys.googleapis.com --project=${MCP_PROJECT}`
    };
  } catch (error) {
    return {
      name: 'API Keys API',
      status: 'WARN',
      message: 'Could not verify API enablement. Please check manually.',
      command: `gcloud services list --enabled --project=${MCP_PROJECT} | grep apikeys`
    };
  }
});

// Check 3: Secret Manager API Enabled
runCheck('Secret Manager API', () => {
  try {
    const services = execCommand(`gcloud services list --enabled --project=${MCP_PROJECT} --format="value(name)"`);
    const isEnabled = services.includes('secretmanager.googleapis.com');
    
    return {
      name: 'Secret Manager API',
      status: isEnabled ? 'PASS' : 'FAIL',
      message: isEnabled ? 'Secret Manager API is enabled' : 'Secret Manager API is not enabled',
      command: isEnabled ? '' : `gcloud services enable secretmanager.googleapis.com --project=${MCP_PROJECT}`
    };
  } catch (error) {
    return {
      name: 'Secret Manager API',
      status: 'WARN',
      message: 'Could not verify API enablement. Please check manually.',
      command: `gcloud services list --enabled --project=${MCP_PROJECT} | grep secretmanager`
    };
  }
});

// Check 4: Environment Variables
runCheck('Environment Variables', () => {
  const requiredVars = ['MCP_PROJECT', 'TENANT_PROJECT', 'GOOGLE_CLOUD_PROJECT', 'MCP_RULES_BASE'];
  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length === 0) {
    return {
      name: 'Environment Variables',
      status: 'PASS',
      message: 'All required environment variables are set'
    };
  } else {
    return {
      name: 'Environment Variables',
      status: 'FAIL',
      message: `Missing environment variables: ${missing.join(', ')}`
    };
  }
});

// Check 4.1: MCP Rules Base URL Validation
runCheck('MCP Rules Base URL', () => {
  const mcpRulesBase = process.env.MCP_RULES_BASE;

  if (!mcpRulesBase) {
    return {
      name: 'MCP Rules Base URL',
      status: 'FAIL',
      message: 'MCP_RULES_BASE environment variable is not set'
    };
  }

  if (!mcpRulesBase.startsWith('https://mcp-rules-gateway-')) {
    return {
      name: 'MCP Rules Base URL',
      status: 'FAIL',
      message: `MCP_RULES_BASE must start with 'https://mcp-rules-gateway-', got: ${mcpRulesBase}`
    };
  }

  return {
    name: 'MCP Rules Base URL',
    status: 'PASS',
    message: `MCP Rules Base URL is correctly configured: ${mcpRulesBase}`
  };
});

// Check 5: Google Cloud Authentication
runCheck('Google Cloud Auth', () => {
  try {
    const account = execCommand('gcloud auth list --filter=status:ACTIVE --format="value(account)"').trim();
    
    if (account) {
      return {
        name: 'Google Cloud Auth',
        status: 'PASS',
        message: `Authenticated as: ${account}`
      };
    } else {
      return {
        name: 'Google Cloud Auth',
        status: 'FAIL',
        message: 'No active Google Cloud authentication found',
        command: 'gcloud auth login'
      };
    }
  } catch (error) {
    return {
      name: 'Google Cloud Auth',
      status: 'FAIL',
      message: 'Google Cloud CLI not available or not authenticated',
      command: 'gcloud auth login'
    };
  }
});

// Check 6: Dependencies Installed
runCheck('Dependencies', () => {
  const packageJsonPath = './package.json';
  
  if (!existsSync(packageJsonPath)) {
    return {
      name: 'Dependencies',
      status: 'FAIL',
      message: 'package.json not found'
    };
  }

  try {
    const packageJson = require('../package.json');
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    const requiredDeps = ['@google-cloud/apikeys', '@google-cloud/secret-manager'];
    const missing = requiredDeps.filter(dep => !deps[dep]);
    
    if (missing.length === 0) {
      return {
        name: 'Dependencies',
        status: 'PASS',
        message: 'All required Google Cloud dependencies are installed'
      };
    } else {
      return {
        name: 'Dependencies',
        status: 'FAIL',
        message: `Missing dependencies: ${missing.join(', ')}`,
        command: `npm install ${missing.join(' ')}`
      };
    }
  } catch (error) {
    return {
      name: 'Dependencies',
      status: 'FAIL',
      message: 'Could not read package.json'
    };
  }
});

// Print results
console.log('\n🔍 MCP Setup Verification Results\n');
console.log('='.repeat(80));

results.forEach(result => {
  const icon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌';
  console.log(`${icon} ${result.name}: ${result.message}`);
  
  if (result.command && result.status !== 'PASS') {
    console.log(`   💡 Fix: ${result.command}`);
  }
  console.log();
});

// Summary
const passed = results.filter(r => r.status === 'PASS').length;
const failed = results.filter(r => r.status === 'FAIL').length;
const warned = results.filter(r => r.status === 'WARN').length;

console.log('='.repeat(80));
console.log(`📊 Summary: ${passed} passed, ${failed} failed, ${warned} warnings`);

if (failed === 0) {
  console.log('🎉 All critical checks passed! Ready for staging deployment.');
} else {
  console.log('🚨 Please fix the failed checks before deploying to staging.');
  process.exit(1);
}
