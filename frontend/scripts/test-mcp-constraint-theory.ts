#!/usr/bin/env ts-node
/**
 * Test if the constraint is related to MCP status or other field combinations
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function testMcpConstraintTheory() {
  console.log('🔍 Testing MCP Constraint Theory');
  console.log('='.repeat(40));

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  try {
    // Get existing record for reference
    const { data: existingRecords } = await supabase
      .schema('tenants')
      .from('firms')
      .select('*')
      .limit(1);

    const existing = existingRecords?.[0];
    if (!existing) {
      console.log('❌ No existing record found');
      return;
    }

    console.log('📋 Existing record analysis:');
    console.log(`   subscription_status: ${existing.subscription_status}`);
    console.log(`   mcp_status: ${existing.mcp_status}`);
    console.log(`   status: ${existing.status}`);
    console.log(`   verification_status: ${existing.verification_status}`);

    // Theory: Maybe the constraint checks that subscription_status can only be 'active' 
    // when mcp_status is also 'active' or some other specific value
    
    console.log('\n🧪 Theory 1: Testing subscription_status with different mcp_status values...');
    
    const mcpStatusTests = [
      { subscription_status: 'active', mcp_status: 'active' },
      { subscription_status: 'active', mcp_status: 'pending_key' },
      { subscription_status: 'active', mcp_status: 'inactive' },
      { subscription_status: 'trialing', mcp_status: 'active' },
      { subscription_status: 'trialing', mcp_status: 'pending_key' },
      { subscription_status: 'trialing', mcp_status: null },
    ];

    for (let i = 0; i < mcpStatusTests.length; i++) {
      const test = mcpStatusTests[i];
      const testData = {
        tenant_id: `00000000-0000-0000-0000-00000000004${i}`,
        name: `MCP Test ${i}`,
        state_bar_number: `MCP${i}`,
        firm_type: 'Solo Practice',
        primary_email: `mcptest${i}@test.com`,
        phone: '************',
        admin_user_id: existing.admin_user_id,
        subscription_status: test.subscription_status,
        subscription_tier: 'basic',
        status: 'active',
        verification_status: 'pending',
        mcp_status: test.mcp_status,
      };

      const { error } = await supabase
        .schema('tenants')
        .from('firms')
        .insert(testData);

      if (error) {
        console.log(`❌ subscription_status="${test.subscription_status}", mcp_status="${test.mcp_status}" failed: ${error.message}`);
      } else {
        console.log(`✅ subscription_status="${test.subscription_status}", mcp_status="${test.mcp_status}" succeeded!`);
        await supabase.schema('tenants').from('firms').delete().eq('tenant_id', testData.tenant_id);
      }
    }

    // Theory 2: Maybe it's about the combination of status fields
    console.log('\n🧪 Theory 2: Testing different status field combinations...');
    
    const statusCombinations = [
      { status: 'active', verification_status: 'verified', subscription_status: 'active' },
      { status: 'pending', verification_status: 'pending', subscription_status: 'trialing' },
      { status: 'inactive', verification_status: 'pending', subscription_status: 'inactive' },
    ];

    for (let i = 0; i < statusCombinations.length; i++) {
      const combo = statusCombinations[i];
      const testData = {
        tenant_id: `00000000-0000-0000-0000-00000000005${i}`,
        name: `Status Combo Test ${i}`,
        state_bar_number: `SC${i}`,
        firm_type: 'Solo Practice',
        primary_email: `statuscombo${i}@test.com`,
        phone: '************',
        admin_user_id: existing.admin_user_id,
        subscription_tier: 'basic',
        ...combo,
      };

      const { error } = await supabase
        .schema('tenants')
        .from('firms')
        .insert(testData);

      if (error) {
        console.log(`❌ Combo ${JSON.stringify(combo)} failed: ${error.message}`);
      } else {
        console.log(`✅ Combo ${JSON.stringify(combo)} succeeded!`);
        await supabase.schema('tenants').from('firms').delete().eq('tenant_id', testData.tenant_id);
      }
    }

    // Theory 3: Maybe there's a specific constraint about new vs existing records
    console.log('\n🧪 Theory 3: Testing if constraint is about record age or creation...');
    
    // Try to copy an existing record exactly but with new IDs
    const exactCopyData = {
      ...existing,
      id: undefined, // Let DB generate
      tenant_id: '00000000-0000-0000-0000-000000000060',
      name: 'Exact Copy Test',
      state_bar_number: 'COPY456',
      primary_email: '<EMAIL>',
      created_at: existing.created_at, // Use same creation time
      updated_at: existing.updated_at, // Use same update time
    };

    const { error: copyError } = await supabase
      .schema('tenants')
      .from('firms')
      .insert(exactCopyData);

    if (copyError) {
      console.log('❌ Exact copy with same timestamps failed:', copyError.message);
    } else {
      console.log('✅ Exact copy with same timestamps succeeded!');
      await supabase.schema('tenants').from('firms').delete().eq('tenant_id', exactCopyData.tenant_id);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testMcpConstraintTheory().catch(console.error);
