#!/usr/bin/env ts-node
/**
 * Compare our test data with existing successful records
 * to identify what might be missing
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY!;

async function compareWithExisting() {
  console.log('🔍 Comparing Test Data with Existing Successful Records');
  console.log('='.repeat(65));

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  try {
    // Get a successful existing record
    const { data: existingRecords, error: fetchError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('*')
      .limit(1);

    if (fetchError || !existingRecords || existingRecords.length === 0) {
      console.log('❌ Could not fetch existing record:', fetchError);
      return;
    }

    const existing = existingRecords[0];
    console.log('✅ Found existing successful record:');
    console.log(`   Name: ${existing.name}`);
    console.log(`   Subscription Status: ${existing.subscription_status}`);
    console.log(`   Subscription Tier: ${existing.subscription_tier}`);

    // Create our test data in the same format
    const ourTestData = {
      tenant_id: '00000000-0000-0000-0000-000000000004',
      name: 'Our Test Firm',
      state_bar_number: 'TEST456',
      firm_type: 'Partnership',
      primary_email: '<EMAIL>',
      phone: '******-123-4567',
      admin_user_id: existing.admin_user_id, // Use same admin user
      subscription_status: existing.subscription_status, // Use same status
      subscription_tier: existing.subscription_tier, // Use same tier
      address: existing.address, // Use same address format
      practice_areas: existing.practice_areas, // Use same format
      specializations: existing.specializations, // Use same format
      settings: existing.settings, // Use same format
      metadata: existing.metadata, // Use same format
      jurisdiction_settings: existing.jurisdiction_settings, // Use same format
      status: existing.status, // Use same status
      verification_status: existing.verification_status, // Use same verification status
    };

    console.log('\n📊 Field-by-field comparison:');
    console.log('='.repeat(50));

    // Compare each field
    Object.keys(existing).forEach(key => {
      if (key === 'id' || key === 'tenant_id' || key === 'created_at' || key === 'updated_at') {
        return; // Skip auto-generated fields
      }

      const existingValue = existing[key];
      const ourValue = ourTestData[key];
      const match = JSON.stringify(existingValue) === JSON.stringify(ourValue);

      console.log(`${match ? '✅' : '❌'} ${key}:`);
      console.log(`   Existing: ${JSON.stringify(existingValue)}`);
      console.log(`   Our test: ${JSON.stringify(ourValue)}`);
      
      if (!match) {
        console.log(`   ⚠️  DIFFERENCE DETECTED!`);
      }
      console.log('');
    });

    // Now try to insert our test data
    console.log('🧪 Testing insertion with matched data...');
    
    const { data: insertResult, error: insertError } = await supabase
      .schema('tenants')
      .from('firms')
      .insert(ourTestData)
      .select();

    if (insertError) {
      console.log('❌ Insert still failed:', insertError.message);
      console.log('Full error details:', insertError);
      
      // Let's try with even more minimal data - just the absolutely required fields
      console.log('\n🔬 Trying with minimal required fields only...');
      
      const minimalData = {
        tenant_id: '00000000-0000-0000-0000-000000000005',
        name: existing.name + ' Copy', // Slightly different name
        state_bar_number: 'MIN123',
        firm_type: existing.firm_type,
        primary_email: '<EMAIL>',
        phone: existing.phone,
        admin_user_id: existing.admin_user_id,
        subscription_status: existing.subscription_status,
        subscription_tier: existing.subscription_tier,
        status: existing.status,
        verification_status: existing.verification_status,
      };
      
      const { error: minimalError } = await supabase
        .schema('tenants')
        .from('firms')
        .insert(minimalData);
        
      if (minimalError) {
        console.log('❌ Minimal insert also failed:', minimalError.message);
      } else {
        console.log('✅ Minimal insert succeeded! The issue is with optional fields.');
        // Clean up
        await supabase.schema('tenants').from('firms').delete().eq('tenant_id', minimalData.tenant_id);
      }
      
    } else {
      console.log('✅ Insert succeeded!');
      console.log('Inserted record:', insertResult[0]?.name);
      
      // Clean up
      await supabase.schema('tenants').from('firms').delete().eq('tenant_id', ourTestData.tenant_id);
      console.log('🧹 Cleaned up test record');
    }

  } catch (error) {
    console.error('❌ Comparison failed:', error);
  }
}

compareWithExisting().catch(console.error);
