name: Deploy Production

on:
  push:
    branches: [ main ]
    tags: [ 'prod-release-*' ]
  workflow_dispatch:

jobs:
  gateway_apply:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/prod-release-')
    environment: production
    outputs:
      gateway_host: ${{ steps.gateway_deploy.outputs.gateway_host }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Google Cloud CLI for MCP Project
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: texas-laws-personalinjury
          export_default_credentials: true

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"
          terraform_wrapper: false

      - name: Deploy API Gateway with Terraform
        id: gateway_deploy
        working-directory: infra/terraform
        run: |
          # Initialize Terraform
          terraform init -input=false

          # Plan with detailed exit code (0=no changes, 1=error, 2=changes)
          echo "🔍 Running terraform plan..."
          terraform plan -lock=false \
            -var="cloud_run_url=${{ secrets.CLOUD_RUN_URL }}" \
            -detailed-exitcode

          PLAN_EXIT_CODE=$?
          echo "Plan exit code: $PLAN_EXIT_CODE"

          # Exit code 2 means changes detected - this should fail for cut-over
          if [ $PLAN_EXIT_CODE -eq 2 ]; then
            echo "❌ Terraform plan shows changes - this should be a no-op cut-over"
            echo "Please ensure infrastructure is already deployed before cut-over"
            exit 1
          elif [ $PLAN_EXIT_CODE -eq 1 ]; then
            echo "❌ Terraform plan failed with error"
            exit 1
          fi

          echo "✅ Terraform plan shows no changes - proceeding with apply"

          # Apply changes (should only update state/provider references)
          echo "🚀 Applying Terraform configuration..."
          terraform apply -auto-approve \
            -var="cloud_run_url=${{ secrets.CLOUD_RUN_URL }}"

          # Export gateway host for next steps
          GATEWAY_HOST=$(terraform output -raw prod_gateway_host)
          echo "gateway_host=https://$GATEWAY_HOST" >> $GITHUB_OUTPUT
          echo "🌐 Gateway host: https://$GATEWAY_HOST"

      - name: Verify API Gateway
        run: |
          # Get API key from Secret Manager for verification
          echo "🔑 Retrieving API key from Secret Manager..."
          API_KEY=$(gcloud secrets versions access latest \
            --secret=mcp-key-sandbox-test \
            --project=texas-laws-personalinjury)

          # Export for verification script
          export API_KEY="$API_KEY"
          export GATEWAY_HOST="${{ steps.gateway_deploy.outputs.gateway_host }}"

          echo "🧪 Running API Gateway verification..."
          # Run verification script
          chmod +x scripts/verify-api-gateway-prod.sh
          scripts/verify-api-gateway-prod.sh --skip-performance --skip-rate-limit

          echo "✅ API Gateway verification completed successfully"

  deploy-production:
    runs-on: ubuntu-latest
    needs: gateway_apply
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/prod-release-')
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Google Cloud CLI for Backend Project
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}
          export_default_credentials: true

      - name: Configure Docker for GCR
        run: gcloud auth configure-docker

      - name: Build and push Docker image
        run: |
          IMAGE_TAG=$(git rev-parse --short HEAD)
          docker build -t gcr.io/${{ secrets.GCP_PROJECT_ID }}/ailex-backend:${IMAGE_TAG} .
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/ailex-backend:${IMAGE_TAG}
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV

      - name: Update Backend Environment with Gateway Host
        run: |
          # Use gateway host from previous job
          GATEWAY_HOST="${{ needs.gateway_apply.outputs.gateway_host }}"
          echo "🔄 Updating Cloud Run service with gateway host: $GATEWAY_HOST"

          gcloud run services update ailex-backend-prod \
            --region=us-central1 \
            --project=${{ secrets.GCP_PROJECT_ID }} \
            --set-env-vars MCP_RULES_BASE=$GATEWAY_HOST

          echo "✅ Backend environment updated with gateway host"

      - name: Deploy to Cloud Run Production
        run: |
          # Use gateway host from previous job
          GATEWAY_HOST="${{ needs.gateway_apply.outputs.gateway_host }}"

          gcloud run deploy ailex-backend-prod \
            --image gcr.io/${{ secrets.GCP_PROJECT_ID }}/ailex-backend:${{ env.IMAGE_TAG }} \
            --region us-central1 \
            --platform managed \
            --allow-unauthenticated \
            --set-env-vars MCP_RULES_BASE=$GATEWAY_HOST \
            --set-env-vars FEATURE_MCP_RULES_ENGINE=true \
            --set-env-vars GOOGLE_CLOUD_PROJECT=${{ secrets.GOOGLE_CLOUD_PROJECT }} \
            --set-env-vars MCP_PROJECT=${{ secrets.MCP_PROJECT }} \
            --set-env-vars TENANT_PROJECT=${{ secrets.TENANT_PROJECT }} \
            --update-secrets SUPABASE_SERVICE_KEY=SUPABASE_SERVICE_KEY:latest \
            --update-secrets OPENAI_API_KEY=OPENAI_API_KEY:latest \
            --project ${{ secrets.GCP_PROJECT_ID }}

      - name: Verify deployment
        run: |
          # Wait for deployment to be ready
          sleep 30
          
          # Get the service URL
          SERVICE_URL=$(gcloud run services describe ailex-backend-prod \
            --region us-central1 \
            --project ${{ secrets.GCP_PROJECT_ID }} \
            --format 'value(status.url)')
          
          # Health check
          curl -f "${SERVICE_URL}/health" || exit 1
          
          echo "✅ Production deployment successful"
          echo "🔗 Service URL: ${SERVICE_URL}"

      - name: Final MCP Rules integration test
        run: |
          # Use gateway host from previous job
          GATEWAY_HOST="${{ needs.gateway_apply.outputs.gateway_host }}"

          # Test MCP Rules endpoint with verification script
          API_KEY=$(gcloud secrets versions access latest \
            --secret=mcp-key-sandbox-test \
            --project=${{ secrets.MCP_PROJECT }})

          echo "🧪 Running final MCP Rules integration test..."
          chmod +x scripts/verify-api-gateway-prod.sh
          scripts/verify-api-gateway-prod.sh --gateway-host "$GATEWAY_HOST" --api-key "$API_KEY" --skip-performance --skip-rate-limit

          echo "✅ Final MCP Rules integration test passed"

      - name: Post GitHub Comment Summary
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const gatewayHost = "${{ needs.gateway_apply.outputs.gateway_host }}";
            const imageTag = "${{ env.IMAGE_TAG }}";

            const comment = `## 🎉 Production Deployment Successful

            **Gateway Cut-over Completed:**
            - ✅ Gateway redeployed and verified
            - ✅ Backend environment updated
            - ✅ All health checks passed

            **Deployment Details:**
            - 📊 Image: \`gcr.io/${{ secrets.GCP_PROJECT_ID }}/ailex-backend:${imageTag}\`
            - 🌐 MCP Rules Base: \`${gatewayHost}\`
            - 🚀 Deployed at: ${new Date().toISOString()}

            **Cut-over Status:** Vercel → Google API Gateway ✅ Complete`;

            // Post comment on PR if this is a PR, otherwise on the commit
            if (context.payload.pull_request) {
              github.rest.issues.createComment({
                issue_number: context.payload.pull_request.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } else {
              github.rest.repos.createCommitComment({
                commit_sha: context.sha,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

      - name: Notify deployment success
        if: success()
        run: |
          GATEWAY_HOST="${{ needs.gateway_apply.outputs.gateway_host }}"

          echo "🎉 Production deployment completed successfully!"
          echo "📊 Image: gcr.io/${{ secrets.GCP_PROJECT_ID }}/ailex-backend:${{ env.IMAGE_TAG }}"
          echo "🔗 MCP Rules Base: $GATEWAY_HOST"
          echo "✅ Gateway redeployed ✅, backend env updated."
