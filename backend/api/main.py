"""
Main FastAPI application entry point.

This module initializes the FastAPI application and registers all routers.
"""

# Ensure .env is loaded before importing any route or service modules
from dotenv import load_dotenv

# Load .env and override any existing vars to pick up updated values
load_dotenv(override=True)

import logging
import os

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response

from backend.metrics import get_metrics_output

# Import metrics components
from backend.middleware.metrics_middleware import CalendarMetricsMiddleware

# Import webhook retry task
from backend.tasks.webhook_retry import schedule_webhook_retry_task

# Import all route modules
from .copilotkit_route import router as copilotkit_router
from .routes import scheduling_router, webhook_router
from .routes.admin import llm_router
from .routes.avricons_intake import router as avricons_intake_router
from .routes.calendar import router as calendar_router
from .routes.mcp_metrics import router as mcp_metrics_router
from .routes.tenants import router as tenants_router
from .subscription_api import router as subscription_router

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Initialization function to create the FastAPI app
def create_app():
    # Create FastAPI app
    app = FastAPI(
        title="AiLex API",
        description="API for AiLex Legal Assistant",
        version="1.0.0",
    )

    # Configure CORS
    origins = os.getenv("CORS_ORIGINS", "*").split(",")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add metrics middleware for calendar routes
    app.add_middleware(CalendarMetricsMiddleware)

    # Register routers
    app.include_router(copilotkit_router)
    app.include_router(webhook_router)
    app.include_router(scheduling_router)
    app.include_router(llm_router)
    app.include_router(avricons_intake_router)
    app.include_router(calendar_router)
    app.include_router(mcp_metrics_router)
    app.include_router(tenants_router)
    app.include_router(subscription_router)

    # Add startup and shutdown events
    @app.on_event("startup")
    async def startup_event():
        logger.info("Starting AiLex API")

    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("Shutting down AiLex API")

    # Add metrics endpoint
    @app.get("/metrics")
    async def metrics():
        """Prometheus metrics endpoint."""
        metrics_output = get_metrics_output()
        return Response(
            content=metrics_output,
            media_type="text/plain; version=0.0.4"
        )

    # Register webhook retry task
    schedule_webhook_retry_task(app)

    return app


# Create the app instance to be used by ASGI servers (like Uvicorn)
app = create_app()

# For direct execution (development)
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
