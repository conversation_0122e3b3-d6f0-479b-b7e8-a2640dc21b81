"""
Configuration settings for the backend.

This module provides configuration settings for the backend, loaded from environment variables.
"""

import os


class Settings:
    """Settings class for the backend."""

    def __init__(self):
        """Initialize settings from environment variables."""
        # Voice Receptionist API settings
        self.voice_receptionist_api_url = os.getenv("VOICE_RECEPTIONIST_API_URL", "https://api.example.com/webhook")
        self.voice_receptionist_api_key = os.getenv("VOICE_RECEPTIONIST_API_KEY", "test-api-key")
        self.voice_receptionist_hmac_secret = os.getenv("VOICE_RECEPTIONIST_HMAC_SECRET", "test-secret")

        # Auth Service settings
        self.auth_service_base = os.getenv("AUTH_SERVICE_BASE", "https://ailex-auth.fly.dev")
        self.ailex_firm_id = os.getenv("AILEX_FIRM_ID", "default-firm")

        # MCP Rules Engine settings
        self.mcp_rules_base = os.getenv("MCP_RULES_BASE")
        if not self.mcp_rules_base:
            raise ValueError("MCP_RULES_BASE environment variable is required but not set")

        self.feature_mcp_rules_engine = os.getenv("FEATURE_MCP_RULES_ENGINE", "false").lower() == "true"

        # Other settings can be added here as needed

# Create a singleton instance
settings = Settings()
