# MCP Rules Engine Terraform Variables
# Copy this file to terraform.tfvars and update with your actual values

# Required: Production Cloud Run URL for MCP Rules Engine
cloud_run_url = "https://mcp-prod-************.us-central1.run.app"

# Optional: Override default project IDs
project_id = "newtexaslaw-*************"
mcp_project_id = "texas-laws-personalinjury"

# Optional: Override default region
# region = "us-central1"

# Optional: Add additional tenants
# tenants = ["pilot-smith", "demo-tenant", "sandbox-test", "new-tenant"]

# Optional: Custom domain for API Gateway
# api_gateway_domain = "rules.ailexlaw.com"

# Optional: Override backend service account
# backend_service_account = "<EMAIL>"

# Optional: Environment and monitoring settings
# environment = "production"
# enable_monitoring = true
# gateway_timeout = 30
# rate_limit_per_minute = 100

