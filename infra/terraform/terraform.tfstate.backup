{"version": 4, "terraform_version": "1.5.7", "serial": 88, "lineage": "ca5901af-c120-8608-db7b-51fbfa241a41", "outputs": {"api_config_id": {"value": null, "type": "string"}, "api_gateway_id": {"value": null, "type": "string"}, "api_gateway_url": {"value": null, "type": "string"}, "env_var_secret_ids": {"value": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "type": ["object", {"mcp_rules_base_prod": "string", "mcp_rules_base_staging": "string"}]}, "env_var_secret_names": {"value": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "type": ["object", {"mcp_rules_base_prod": "string", "mcp_rules_base_staging": "string"}]}, "mcp_api_key_names": {"value": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "type": ["object", {"demo-tenant": "dynamic", "pilot-smith": "dynamic", "sandbox-test": "dynamic"}]}, "mcp_key_rotator_email": {"value": null, "type": "string"}, "mcp_secret_ids": {"value": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "type": ["object", {"demo-tenant": "dynamic", "pilot-smith": "dynamic", "sandbox-test": "dynamic"}]}, "mcp_secret_names": {"value": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "type": ["object", {"demo-tenant": "dynamic", "pilot-smith": "dynamic", "sandbox-test": "dynamic"}]}, "prod_gateway_host": {"value": null, "type": "string"}, "ssl_certificate_id": {"value": null, "type": "string"}}, "resources": [{"mode": "managed", "type": "google_api_gateway_api", "name": "mcp_rules_api", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"].mcp", "instances": [{"schema_version": 0, "attributes": {"api_id": "mcp-rules-gateway", "create_time": "2025-06-12T10:05:15.989929003Z", "display_name": "mcp-rules-gateway", "effective_labels": {}, "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "labels": {}, "managed_service": "mcp-rules-gateway-3jnyyfifdjilf.apigateway.texas-laws-personalinjury.cloud.goog", "name": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "project": "texas-laws-personalinjury", "terraform_labels": {}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "function_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_cloudfunctions2_function", "name": "tenant_onboard", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_firestore_user", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_run_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_project_service", "name": "apigateway", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/apigateway.googleapis.com", "project": "texas-laws-personalinjury", "service": "apigateway.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_project_service", "name": "servicecontrol", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicecontrol.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicecontrol.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_project_service", "name": "servicemanagement", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicemanagement.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicemanagement.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_service_account_iam_member", "name": "allow_impersonation", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}], "check_results": null}