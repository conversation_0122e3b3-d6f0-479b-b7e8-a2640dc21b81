# MCP Rules Engine - Production API Gateway Deployment Guide

## Overview

This document provides comprehensive instructions for deploying and managing the production API Gateway for the MCP Rules Engine. The API Gateway serves as the secure, scalable entry point for legal deadline calculations.

## Architecture

```
AiLex Backend → API Gateway → Cloud Run (MCP Rules Engine)
                    ↓
              Cloud Logging & Monitoring
                    ↓
              Secret Manager (API Keys)
```

**Note**: OpenAPI spec now rendered with native `templatefile()`, no external template provider required.

## API Endpoints

### Production Gateway URL
`https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev`

### Available Endpoints

#### 1. Health Check (Public)
- **URL**: `GET /health`
- **Authentication**: None required (public for load balancer monitoring)
- **Response**: `{"status":"OK","timestamp":"2025-06-21T20:43:26.293Z"}`

#### 2. MCP Rules Engine (Protected)
- **URL**: `POST /mcp/run`
- **Authentication**: Required (`x-api-key` header)
- **Content-Type**: `application/json`
- **Request Body**:
  ```json
  {
    "toolName": "calculate_deadlines",
    "params": {
      "jurisdiction": "TX_STATE",
      "triggerCode": "SERVICE_OF_PROCESS",
      "startDate": "2025-08-01"
    }
  }
  ```
- **Response**:
  ```json
  {
    "result": {
      "deadlines": [
        {
          "deadlineCode": "ANSWER_DUE",
          "deadlineDate": "2025-08-21",
          "ruleCitation": "Tex. R. Civ. P. 99(b)",
          "calcSteps": [
            "Start 2025-08-01",
            "+20 calendar days → 2025-08-21"
          ]
        }
      ]
    }
  }
  ```

### Security Configuration
- **Health endpoint**: Public access (no API key required)
- **MCP endpoints**: API key authentication required
- **API keys**: Stored in Secret Manager, one per tenant
- **Rate limiting**: 100 requests/minute per API key (configurable)

## Prerequisites

### Required Tools
- `gcloud` CLI (authenticated with appropriate permissions)
- `terraform` (>= 1.0)
- Access to `texas-laws-personalinjury` GCP project

### Required Permissions
- `roles/apigateway.admin`
- `roles/servicemanagement.admin`
- `roles/compute.admin` (for SSL certificates)
- `roles/monitoring.admin` (for alerting)
- `roles/run.admin` (for Cloud Run permissions)

### Environment Setup
```bash
# Set project
gcloud config set project texas-laws-personalinjury

# Authenticate
gcloud auth application-default login

# Verify access
gcloud projects describe texas-laws-personalinjury
```

## Deployment Process

### Phase 1: Infrastructure Deployment

#### 1. Deploy Terraform Infrastructure
```bash
cd infra/terraform

# Initialize Terraform (first time only)
terraform init

# Review planned changes
terraform plan -var="cloud_run_url=https://your-cloud-run-url" \
               -var="backend_service_account=<EMAIL>"

# Apply infrastructure
terraform apply -var="cloud_run_url=https://your-cloud-run-url" \
                -var="backend_service_account=<EMAIL>"
```

#### 2. Manual API Configuration (Alternative Method)
If Terraform deployment fails, use manual gcloud commands:

```bash
# Create API Gateway API (one-time setup)
gcloud api-gateway apis create mcp-rules-gateway \
    --project=texas-laws-personalinjury

# Create API configuration with correct backend authentication
# CRITICAL: The --backend-auth-service-account parameter is REQUIRED for Cloud Run authentication
gcloud api-gateway api-configs create mcp-rules-config-prod-final \
    --api=mcp-rules-gateway \
    --openapi-spec=api/mcp-rules-config-prod.yaml \
    --project=texas-laws-personalinjury \
    --backend-auth-service-account=<EMAIL> \
    --display-name="MCP Rules Engine Production Config (With Backend Auth)"

# Create/Update the gateway
gcloud api-gateway gateways create mcp-rules-gateway-prod \
    --api=mcp-rules-gateway \
    --api-config=mcp-rules-config-prod-final \
    --location=us-central1 \
    --project=texas-laws-personalinjury
```

### Phase 2: Configuration Updates

#### Update Existing Gateway
```bash
# Update gateway with new configuration
gcloud api-gateway gateways update mcp-rules-gateway-prod \
    --api=mcp-rules-gateway \
    --api-config=mcp-rules-config-prod \
    --location=us-central1 \
    --project=texas-laws-personalinjury
```

#### Monitor Deployment Status
```bash
# Check gateway status
gcloud api-gateway gateways describe mcp-rules-gateway-prod \
    --location=us-central1 \
    --project=texas-laws-personalinjury

# Wait for ACTIVE state (typically 30-60 seconds)
while [[ $(gcloud api-gateway gateways describe mcp-rules-gateway-prod \
    --location=us-central1 \
    --project=texas-laws-personalinjury \
    --format="value(state)") != "ACTIVE" ]]; do
  echo "Waiting for gateway to become active..."
  sleep 10
done
echo "Gateway is now ACTIVE"
```

### Phase 3: Smoke Testing

#### 1. Health Check Test (Public Endpoint)
```bash
# Test health endpoint (NO AUTHENTICATION REQUIRED)
curl -f https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev/health

# Expected response:
# {"status":"OK","timestamp":"2025-06-21T20:43:26.293Z"}

# This endpoint is public for load balancer health checks
```

#### 2. API Key Retrieval and Testing
```bash
# Retrieve test API key
API_KEY=$(gcloud secrets versions access latest \
          --secret=mcp-key-sandbox-test \
          --project=texas-laws-personalinjury)

# Test MCP endpoint
curl -s -X POST https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev/mcp/run \
     -H 'Content-Type: application/json' \
     -H "x-api-key: $API_KEY" \
     -d '{
       "toolName": "calculate_deadlines",
       "params": {
         "jurisdiction": "TX_STATE",
         "triggerCode": "SERVICE_OF_PROCESS",
         "startDate": "2025-08-01"
       }
     }' | jq .

# Expected response should include deadlines array
```

#### 3. Error Handling Tests
```bash
# Test invalid API key
curl -s -X POST https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev/mcp/run \
     -H 'Content-Type: application/json' \
     -H "x-api-key: invalid-key" \
     -d '{"toolName": "calculate_deadlines", "params": {}}' \
     | jq .

# Expected: 401 Unauthorized

# Test missing parameters
curl -s -X POST https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev/mcp/run \
     -H 'Content-Type: application/json' \
     -H "x-api-key: $API_KEY" \
     -d '{"toolName": "calculate_deadlines"}' \
     | jq .

# Expected: 400 Bad Request
```

### Phase 4: Backend Integration

#### Update Backend Environment Variables
```bash
# Update Cloud Run service to use new gateway
gcloud run services update ailex-backend-prod \
  --region=us-central1 \
  --set-env-vars MCP_RULES_BASE=https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev \
  --project=new-texas-laws

# Verify deployment
gcloud run services describe ailex-backend-prod \
  --region=us-central1 \
  --project=new-texas-laws \
  --format="value(spec.template.spec.containers[0].env[?name=='MCP_RULES_BASE'].value)"
```

## Custom Domain Setup (Optional)

### 1. DNS Configuration
```bash
# Create DNS mapping (requires domain ownership verification)
gcloud domains mappings create rules.ailexlaw.com \
    --certificate-id=mcp-rules-gateway-ssl \
    --project=texas-laws-personalinjury
```

### 2. SSL Certificate Management
```bash
# Check certificate status
gcloud compute ssl-certificates describe mcp-rules-gateway-ssl \
    --global \
    --project=texas-laws-personalinjury

# Certificate will be automatically provisioned once DNS is configured
```

## Monitoring and Alerting

### View Metrics
```bash
# API Gateway metrics
gcloud logging read "resource.type=api_gateway" \
    --project=texas-laws-personalinjury \
    --limit=50

# Error rate monitoring
gcloud alpha monitoring policies list \
    --filter="displayName:MCP Rules Gateway" \
    --project=texas-laws-personalinjury
```

### Set Up Alerts
The Terraform configuration includes:
- High error rate alert (>2%)
- High latency alert (P95 >700ms)
- Circuit breaker monitoring

## Rollback Procedures

### Emergency Rollback to Previous Configuration

#### 1. Identify Previous Configuration
```bash
# List API configurations
gcloud api-gateway api-configs list \
    --api=mcp-rules-gateway \
    --project=texas-laws-personalinjury

# Note the previous config ID (e.g., mcp-rules-config-prod-v1)
```

#### 2. Rollback Gateway
```bash
# Rollback to previous configuration
gcloud api-gateway gateways update mcp-rules-gateway-prod \
    --api=mcp-rules-gateway \
    --api-config=PREVIOUS_CONFIG_ID \
    --location=us-central1 \
    --project=texas-laws-personalinjury

# Monitor rollback status
gcloud api-gateway gateways describe mcp-rules-gateway-prod \
    --location=us-central1 \
    --project=texas-laws-personalinjury \
    --format="value(state)"
```

#### 3. Rollback Backend Environment
```bash
# Revert backend to previous MCP_RULES_BASE
gcloud run services update ailex-backend-prod \
  --region=us-central1 \
  --set-env-vars MCP_RULES_BASE=https://previous-gateway-url \
  --project=new-texas-laws
```

### Complete Infrastructure Rollback
```bash
# Use Terraform to rollback infrastructure changes
cd infra/terraform

# Revert to previous Terraform state
terraform plan -destroy -target=google_api_gateway_gateway.mcp_rules_gateway
terraform apply -destroy -target=google_api_gateway_gateway.mcp_rules_gateway

# Recreate with previous configuration
terraform apply -var="cloud_run_url=https://previous-url"
```

## Troubleshooting

### Common Issues

#### 1. Gateway Stuck in CREATING State
```bash
# Check operation status
gcloud api-gateway operations list \
    --filter="metadata.target:mcp-rules-gateway-prod" \
    --project=texas-laws-personalinjury

# If stuck, cancel and retry
gcloud api-gateway operations cancel OPERATION_ID \
    --project=texas-laws-personalinjury
```

#### 2. 404 Errors on /mcp/run
- Verify OpenAPI spec has correct path mappings
- Check Cloud Run URL is accessible
- Validate backend service account permissions

#### 3. 403 Forbidden Errors (CRITICAL ISSUE)
**Problem**: API Gateway returns 403 Forbidden for all requests, even health checks.

**Root Cause**: Missing `--backend-auth-service-account` parameter when creating API config.

**Solution**:
```bash
# 1. Verify the service account exists and has Cloud Run invoker permissions
gcloud iam service-<NAME_EMAIL>

# 2. Ensure Cloud Run service has correct IAM binding
gcloud run services add-iam-policy-binding mcp-prod \
  --region=us-central1 \
  --project=texas-laws-personalinjury \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/run.invoker"

# 3. Recreate API config with backend authentication
gcloud api-gateway api-configs create mcp-rules-config-prod-fixed \
    --api=mcp-rules-gateway \
    --openapi-spec=api/mcp-rules-config-prod.yaml \
    --project=texas-laws-personalinjury \
    --backend-auth-service-account=<EMAIL>

# 4. Update gateway to use the fixed config
gcloud api-gateway gateways update mcp-rules-gateway-prod \
    --api-config=projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway/configs/mcp-rules-config-prod-fixed \
    --location=us-central1 \
    --project=texas-laws-personalinjury
```

#### 4. 401/403 API Key Authentication Errors
- Verify API keys are correctly provisioned
- Check Secret Manager permissions
- Validate x-api-key header format

#### 4. High Latency Issues
- Monitor Cloud Run cold starts
- Check backend service scaling configuration
- Review API Gateway timeout settings

### Debug Commands
```bash
# View API Gateway logs
gcloud logging read "resource.type=api_gateway AND resource.labels.gateway_id=mcp-rules-gateway-prod" \
    --project=texas-laws-personalinjury \
    --limit=100

# Check Cloud Run logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=mcp-rules-prod" \
    --project=texas-laws-personalinjury \
    --limit=100

# Test connectivity
curl -v https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev/health
```

## Security Considerations

### API Key Management
- API keys are stored in Secret Manager
- Keys are rotated annually
- Each tenant has isolated API keys
- Usage is tracked per key

### Network Security
- HTTPS only (TLS 1.2+)
- API Gateway provides DDoS protection
- Rate limiting per API key
- Request/response logging

### Compliance
- All requests are logged for audit
- PII is not logged in request bodies
- Retention policies follow legal requirements

## Maintenance

### Regular Tasks
- Monthly: Review error rates and latency metrics
- Quarterly: Update API Gateway configuration if needed
- Annually: Rotate API keys and SSL certificates
- As needed: Scale Cloud Run instances based on usage

### Version Management
- Use semantic versioning for API configurations
- Tag Terraform deployments with release versions
- Maintain rollback configurations for 3 previous versions

## Support

For issues with the API Gateway deployment:
1. Check this documentation first
2. Review monitoring dashboards
3. Check recent deployments and changes
4. Contact the engineering team with specific error messages and request IDs
