"use strict";
/**
 * Cloud Functions entry point
 * Exports all cloud functions for deployment
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.onSupabaseTenantCreate = exports.onTenantCreate = void 0;
var onTenantCreate_1 = require("./onTenantCreate");
Object.defineProperty(exports, "onTenantCreate", { enumerable: true, get: function () { return onTenantCreate_1.onTenantCreate; } });
var onSupabaseTenantCreate_1 = require("./onSupabaseTenantCreate");
Object.defineProperty(exports, "onSupabaseTenantCreate", { enumerable: true, get: function () { return onSupabaseTenantCreate_1.onSupabaseTenantCreate; } });
//# sourceMappingURL=index.js.map